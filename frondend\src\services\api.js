/**
 * Centralized API service for communicating with the Flask backend
 * 
 * This service provides:
 * - Axios instance with proper configuration
 * - Request/response interceptors for error handling
 * - Base URL configuration for different environments
 * - Common HTTP methods (GET, POST, PUT, DELETE)
 * - File upload support
 */

import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: '/api', // This will be proxied to Flask backend by Vite
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor - add auth token if available
api.interceptors.request.use(
  (config) => {
    // Add JWT token if available
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Log request in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        data: config.data,
        params: config.params,
      });
    }
    
    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor - handle common errors
api.interceptors.response.use(
  (response) => {
    // Log response in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
      });
    }
    
    return response;
  },
  (error) => {
    // Log error in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
      });
    }
    
    // Handle common HTTP errors
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // Unauthorized - clear token and redirect to login
          localStorage.removeItem('token');
          // You can dispatch a Redux action here to update auth state
          console.warn('🔒 Unauthorized access - token cleared');
          break;
          
        case 403:
          console.warn('🚫 Forbidden access');
          break;
          
        case 404:
          console.warn('🔍 Resource not found');
          break;
          
        case 500:
          console.error('🔥 Server error');
          break;
          
        default:
          console.error(`🚨 HTTP Error ${status}:`, data?.message || error.message);
      }
      
      // Return a more user-friendly error object
      return Promise.reject({
        status,
        message: data?.error || data?.message || error.message,
        data: data,
      });
    } else if (error.request) {
      // Network error
      console.error('🌐 Network Error:', error.message);
      return Promise.reject({
        status: 0,
        message: 'Network error - please check your connection',
        data: null,
      });
    } else {
      // Other error
      console.error('⚠️ Unknown Error:', error.message);
      return Promise.reject({
        status: -1,
        message: error.message,
        data: null,
      });
    }
  }
);

// API service methods
const apiService = {
  // GET request
  get: (url, params = {}) => {
    return api.get(url, { params });
  },
  
  // POST request
  post: (url, data = {}) => {
    return api.post(url, data);
  },
  
  // PUT request
  put: (url, data = {}) => {
    return api.put(url, data);
  },
  
  // DELETE request
  delete: (url) => {
    return api.delete(url);
  },
  
  // File upload
  uploadFile: (url, formData, onUploadProgress = null) => {
    return api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress,
    });
  },
  
  // Download file
  downloadFile: (url, params = {}) => {
    return api.get(url, {
      params,
      responseType: 'blob',
    });
  },
};

export default apiService;
