from flask import Blueprint, request, jsonify
import pandas as pd
import numpy as np
from db import init_db
from bson import ObjectId
from scipy import stats
from scipy.stats import boxcox, yeojohnson

outlierValue_bp = Blueprint('outlierValue', __name__)

# MongoDB
from flask import Flask
app = Flask(__name__)
mongo = init_db(app)

# ฟังก์ชันสำหรับ Trimming โดยใช้ Z-score
def z_score_trimming(df, feature, threshold):
    z_scores = np.abs(stats.zscore(df[feature]))  # คำนวณ Z-score สำหรับฟีเจอร์
    print("Z-scores:", z_scores)  # ตรวจสอบค่า Z-scores
    # ลบแถวที่มี Z-score เกิน threshold
    df = df[z_scores < threshold]
    print("Data after trimming:", df[feature])  # ตรวจสอบข้อมูลหลังการ Trim
    return df

# ฟังก์ชันสำหรับ Trimming โดยใช้ IQR
def iqr_trimming(df, feature, threshold):
    q1 = np.percentile(df[feature], 25)
    q3 = np.percentile(df[feature], 75)
    iqr = q3 - q1
    upper_limit = q3 + (threshold * iqr)
    lower_limit = q1 - (threshold * iqr)
    # ลบแถวที่ไม่อยู่ในช่วงระหว่าง lower_limit และ upper_limit
    df = df[(df[feature] >= lower_limit) & (df[feature] <= upper_limit)]
    return df

# ฟังก์ชันสำหรับ Trimming โดยใช้ Percentile
def percentile_trimming(df, feature, lower_percentile, upper_percentile):
    lower_limit = np.percentile(df[feature], lower_percentile)
    upper_limit = np.percentile(df[feature], upper_percentile)
    # ลบแถวที่ไม่อยู่ในช่วงระหว่าง lower_limit และ upper_limit
    df = df[(df[feature] >= lower_limit) & (df[feature] <= upper_limit)]
    return df

# ฟังก์ชันสำหรับ Capping โดยใช้ Z-score
def z_score_capping(data, threshold):
    # คำนวณ Z-scores ของข้อมูล
    z_scores = stats.zscore(data)
    # คำนวณ upper limit และ lower limit
    mean = np.mean(data)
    std_dev = np.std(data)
    
    upper_limit = mean + threshold * std_dev  # คำนวณ upper limit
    lower_limit = mean - threshold * std_dev  # คำนวณ lower limit

    # ทำการ cap ข้อมูลโดยใช้ upper และ lower limit
    capped_data = [min(max(x, lower_limit), upper_limit) for x in data]
    return capped_data

# ฟังก์ชันสำหรับ Capping โดยใช้ IQR
def iqr_capping(data, threshold):
    q1 = np.percentile(data, 25)
    q3 = np.percentile(data, 75)
    iqr = q3 - q1
    lower_bound = q1 - threshold * iqr
    upper_bound = q3 + threshold * iqr
    return [min(max(x, lower_bound), upper_bound) for x in data]

# ฟังก์ชันสำหรับ Power Transformation
def apply_power_transformation(data, transformation, lambda_val=None):
    if transformation == "Box-Cox":
        # กรองค่าลบออกจากข้อมูลก่อนใช้ Box-Cox
        data = [x for x in data if x > 0]  
        if len(data) > 0:
            if lambda_val is None:
                # คำนวณ lambda อัตโนมัติถ้าไม่ได้ส่งค่า lambda
                transformed_data, lambda_val = boxcox(data)
                print(f"Calculated optimal lambda: {lambda_val}")  # แสดงค่า lambda ที่คำนวณได้
            else:
                # ใช้ค่า lambda ที่ผู้ใช้ส่งมา
                transformed_data = boxcox(data, lmbda=lambda_val)
            return transformed_data
        else:
            print("Data is empty or contains non-positive values, Box-Cox transformation not applied.")
            return data  # ถ้าไม่มีข้อมูลที่สามารถใช้ Box-Cox ได้

    elif transformation == "Yeo-Johnson":
        if lambda_val is None:
            transformed_data, _ = yeojohnson(data)  # คืนค่าผลลัพธ์แค่ค่าเดียว
        else:
            transformed_data = yeojohnson(data, lmbda=lambda_val)  # ใช้ lambda ที่ผู้ใช้ส่งมา
    return transformed_data

# ฟังก์ชันที่จะตรวจสอบว่า feature_values มีความยาวตรงกับ DataFrame หรือไม่
def check_and_update_length(df, feature, feature_values):
    if len(feature_values) == len(df[feature]):
        df[feature] = feature_values
    else:
        print(f"Error: Length mismatch for feature '{feature}'")
        # วิธีการที่คุณสามารถทำได้เมื่อพบขนาดไม่ตรงกัน
        # ตัวอย่างเช่น: ใช้ค่าเฉลี่ยหรือค่าที่ใกล้เคียงในการเติมข้อมูล
        # ตัวอย่างที่ใช้ค่าเฉลี่ยในการเติมข้อมูล:
        df[feature] = pd.Series([np.mean(feature_values)] * len(df[feature]))

@outlierValue_bp.route('/outlier', methods=['POST'])
def process_outliers():
    # รับข้อมูลจากผู้ใช้
    data = request.get_json()
    dataset_id = data.get("dataset_id")
    features = data['features']
    selected_outlier_option = data['selectedOutlierOption']
    selected_trimming_option = data['selectedTrimmingOption']
    selected_capping_option = data['selectedCappingOption']
    z_score_threshold = data['zScore']
    iqr_threshold = data['iqr']
    percentile_range = data['percentile']
    power_transformation = data['powerTransformation']

    # ตรวจสอบว่า user ส่งค่า lambda หรือไม่
    lambda_val = power_transformation.get('lambda', None)  # หากไม่ส่งจะเป็น None

    # ดึงข้อมูลจาก MongoDB load current ถ้าไม่มีใช้ previous
    current_doc = mongo.db.current.find_one({"dataset_id": ObjectId(dataset_id)})
    if current_doc:
        df = pd.DataFrame(current_doc["data"])
        previous_data = current_doc["data"]
    else:
        previous_doc = mongo.db.previous.find_one({"dataset_id": ObjectId(dataset_id)})
        if not previous_doc:
            return jsonify({"msg": "Dataset not found"}), 404
        df = pd.DataFrame(previous_doc["data"])
        previous_data = previous_doc["data"]

    # สร้างสำเนาของ DataFrame เพื่อให้มั่นใจว่าไม่สูญเสียข้อมูลที่ไม่ได้ถูกแก้ไข
    original_df = df.copy()

    # Trimming
    for feature in features:
        if "Trimming" in selected_outlier_option:
            if selected_trimming_option == "Z-score":
                df = z_score_trimming(df, feature, z_score_threshold)  # ใช้ฟังก์ชัน Trimming โดยใช้ Z-score
            elif selected_trimming_option == "IQR":
                df = iqr_trimming(df, feature, iqr_threshold)
            elif selected_trimming_option == "Percentile":
                df = percentile_trimming(df, feature, percentile_range['lower'], percentile_range['upper'])

        # Capping
        if "Capping" in selected_outlier_option:
            if selected_capping_option == "Z-score":
                feature_values = z_score_capping(df[feature].values.flatten().tolist(), z_score_threshold)
            elif selected_capping_option == "IQR":
                feature_values = iqr_capping(df[feature].values.flatten().tolist(), iqr_threshold)
            elif selected_capping_option == "Percentile":
                lower = np.percentile(df[feature], percentile_range['lower'])
                upper = np.percentile(df[feature], percentile_range['upper'])
                feature_values = [min(max(x, lower), upper) for x in df[feature].values.flatten().tolist()]
            df[feature] = feature_values

        # Power Transformation
        if "Power Transformation" in selected_outlier_option:
            if power_transformation['transformation'] in ["Box-Cox", "Yeo-Johnson"]:
                feature_values = apply_power_transformation(df[feature].values.flatten().tolist(), power_transformation['transformation'], lambda_val)
                df[feature] = feature_values

    # บันทึกข้อมูลที่ผ่านการประมวลผลไปยัง MongoDB
    result_data = df  # The entire DataFrame with processed values

    # Save previous data
    mongo.db.previous.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": previous_data}},
        upsert=True
    )

    # Update current with cleaned data
    mongo.db.current.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": result_data.to_dict(orient='records')}},
        upsert=True
    )

    return jsonify({
        "message": "Outlier processing complete",
        "data": result_data.to_dict(orient='records')
    }), 200
