import React, { useState, useEffect } from "react";

const KNearestNeighborsRegressorForm = ({ onParamsChange }) => {
    const [params, setParams] = useState({
        n_neighbors: 5,
        weights: "distance",
        algorithm: "auto",
    });

    useEffect(() => {
        onParamsChange(params);
    }, [params, onParamsChange]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setParams((prevParams) => ({
            ...prevParams,
            [name]: value,
        }));
    };

    return (
        <div className="space-y-2 p-4 bg-white rounded shadow">

            <div>
                <label className="block text-sm font-medium">n_neighbors:</label>
                <input
                    type="number"
                    name="n_neighbors"
                    value={params.n_neighbors}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="1"
                />
            </div>

            <div>
                <label className="block text-sm font-medium">weights:</label>
                <select
                    name="weights"
                    value={params.weights}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="uniform">uniform</option>
                    <option value="distance">distance</option>
                </select>
            </div>

            <div>
                <label className="block text-sm font-medium">algorithm:</label>
                <select
                    name="algorithm"
                    value={params.algorithm}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="auto">auto</option>
                    <option value="ball_tree">ball_tree</option>
                    <option value="kd_tree">kd_tree</option>
                    <option value="brute">brute</option>
                </select>
            </div>
        </div>
    );
};

export default KNearestNeighborsRegressorForm;
