import React, { useRef, useEffect, useState } from "react";
import { CSVLink } from "react-csv";

const ApplyTable = ({ originalData, cleanedData, tab }) => {
  const MissingColumns = originalData[0] ? Object.keys(originalData[0]) : [];
  const CleansingColumns = cleanedData[0] ? Object.keys(cleanedData[0]) : [];

  const MissingDisplayedRows = originalData
    .map((row, i) => ({
      orig: originalData[i] || {},
      clean: cleanedData[i] || {},
      idx: i,
    }))
    .filter(({ orig }) => Object.values(orig).length > 0);

  const cleansingDisplayedRows = cleanedData
    .map((row, i) => ({
      orig: originalData[i] || {},
      clean: cleanedData[i] || {},
      idx: i,
    }))
    .filter(({ clean }) => Object.values(clean).length > 0);

  // Tooltip position logic
  function useSmartTooltip() {
    const ref = useRef(null);
    const [placement, setPlacement] = useState("top");

    useEffect(() => {
      function handlePosition() {
        if (!ref.current) return;
        const rect = ref.current.getBoundingClientRect();
        const vw = window.innerWidth;
        const vh = window.innerHeight;
        let pos = "top";
        if (rect.left < 0) pos = "right";
        if (rect.right > vw) pos = "left";
        if (rect.top < 0) pos = "bottom";
        if (rect.bottom > vh) pos = "bottom";
        setPlacement(pos);
      }
      handlePosition();
      window.addEventListener("resize", handlePosition);
      return () => window.removeEventListener("resize", handlePosition);
    }, []);

    return [ref, placement];
  }

  const [missingTooltipRef, missingTooltipPlacement] = useSmartTooltip();
  const [changedTooltipRef, changedTooltipPlacement] = useSmartTooltip();

  const [missingTooltipOpen, setMissingTooltipOpen] = useState(false);
  const [changedTooltipOpen, setChangedTooltipOpen] = useState(false);

  const getBeforeApplyTooltipText = (tab) => {
    switch (tab) {
      case "Missing Value":
        return "Value is missing or null";
      case "Outlier":
        return "Value is outlier";
      case "Typo":
        return "Value is typo";
      case "Duplicate":
        return "Value is duplicate";
      case "Encoding":
        return "Value that still not encoding";
      case "Scaling":
        return "Value that still not scaling";
      case "Dimension Reduction":
        return "Value that still not dimensional reduction";
      default:
        return "Value is missing or null";
    }
  };
  
  const getAfterApplyTooltipText = (tab) => {
    switch (tab) {
      case "Missing Value":
        return "Value was imputed or filled";
      case "Outlier":
        return "Value that are not outlier";
      case "Typo":
        return "Value that are not typo";
      case "Duplicate":
        return "Value that are not duplicate";
      case "Encoding":
        return "Value was encoding";
      case "Scaling":
        return "Value was scaling";
      case "Dimension Reduction":
        return "Value was dimensional reduction";
      default:
        return "Value was imputed or filled";
    }
  };

  return (
    <div className="p-6 bg-gradient-to-br from-white via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 rounded-xl shadow-lg">
      <div className="flex items-end mb-6">
        <h1 className="flex-1 text-3xl font-black bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 bg-clip-text text-transparent border-b-4 border-indigo-300 pb-2 tracking-tight drop-shadow-sm">
          {tab}
        </h1>
        <h1 className="flex-1 ml-6 text-3xl font-black bg-gradient-to-r from-green-400 via-blue-400 to-indigo-500 bg-clip-text text-transparent border-b-4 border-green-300 pb-2 tracking-tight drop-shadow-sm text-right">
          After Apply
        </h1>
      </div>
      <div className="flex gap-6">
        {/* Before Data Table */}
        <div className="overflow-auto max-h-96 flex-1 rounded-lg shadow border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
          <table className="min-w-full table-auto border-collapse">
            <thead>
              <tr className="bg-gradient-to-r from-indigo-100 via-purple-100 to-pink-100 dark:from-gray-800 dark:via-gray-900 dark:to-gray-800">
                <th className="px-4 py-3 sticky left-0 bg-inherit z-10 font-bold text-indigo-700 dark:text-indigo-300">
                  #
                </th>
                {MissingColumns.map((col) => (
                  <th
                    key={col}
                    className="px-4 py-3 text-left font-semibold text-gray-700 dark:text-gray-200"
                  >
                    {col}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {MissingDisplayedRows.length === 0 ? (
                <tr>
                  <td
                    colSpan={MissingColumns.length + 1}
                    className="text-center py-6 text-gray-400 dark:text-gray-500"
                  >
                    No missing values found.
                  </td>
                </tr>
              ) : (
                MissingDisplayedRows.map(({ orig, clean, idx }) => (
                  <tr
                    key={idx}
                    className="even:bg-indigo-50 dark:even:bg-gray-800 hover:bg-indigo-100 dark:hover:bg-gray-700 transition"
                  >
                    <td className="px-4 py-2 sticky left-0 bg-inherit z-10 text-gray-600 dark:text-gray-400 font-semibold">
                      {idx + 1}
                    </td>
                    {MissingColumns.map((col) => {
                      const origVal = orig[col];
                      const cleanVal = clean[col];
                      let bgClass =
                        origVal !== cleanVal
                          ? "bg-yellow-200 dark:bg-yellow-800"
                          : "";
                      return (
                        <td
                          key={col}
                          className={`${bgClass} px-4 py-2 text-gray-900 dark:text-gray-100 rounded`}
                        >
                          {origVal || (
                            <span className="italic text-gray-400">NULL</span>
                          )}
                        </td>
                      );
                    })}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Cleansing Data Table */}
        <div className="overflow-auto max-h-96 flex-1 rounded-lg shadow border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
          <table className="min-w-full table-auto border-collapse">
            <thead>
              <tr className="bg-gradient-to-r from-green-100 via-blue-100 to-indigo-100 dark:from-gray-800 dark:via-gray-900 dark:to-gray-800">
                <th className="px-4 py-3 sticky left-0 bg-inherit z-10 font-bold text-green-700 dark:text-green-300">
                  #
                </th>
                {CleansingColumns.map((col) => (
                  <th
                    key={col}
                    className="px-4 py-3 text-left font-semibold text-gray-700 dark:text-gray-200"
                  >
                    {col}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {cleansingDisplayedRows.length === 0 ? (
                <tr>
                  <td
                    colSpan={CleansingColumns.length + 1}
                    className="text-center py-6 text-gray-400 dark:text-gray-500"
                  >
                    No data after cleansing.
                  </td>
                </tr>
              ) : (
                cleansingDisplayedRows.map(({ orig, clean, idx }) => (
                  <tr
                    key={idx}
                    className="even:bg-green-50 dark:even:bg-gray-800 hover:bg-green-100 dark:hover:bg-gray-700 transition"
                  >
                    <td className="px-4 py-2 sticky left-0 bg-inherit z-10 text-gray-600 dark:text-gray-400 font-semibold">
                      {idx + 1}
                    </td>
                    {CleansingColumns.map((col) => {
                      const origVal = orig[col];
                      const cleanVal = clean[col];
                      let bgClass =
                        origVal !== cleanVal
                          ? "bg-green-200 dark:bg-green-800"
                          : "";
                      return (
                        <td
                          key={col}
                          className={`${bgClass} px-4 py-2 text-gray-900 dark:text-gray-100 rounded`}
                        >
                          {cleanVal}
                        </td>
                      );
                    })}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Legend with CSS‑Only Tooltips */}
      <div className="mt-6 flex items-center space-x-8">
        {/* Missing Value Legend */}
        <div
          className="relative group flex items-center cursor-pointer"
          onClick={() => setMissingTooltipOpen((v) => !v)}
          onMouseLeave={() => setMissingTooltipOpen(false)}
        >
          <span className="inline-block w-5 h-5 rounded bg-yellow-200 dark:bg-yellow-800 border border-yellow-400 dark:border-yellow-700 shadow-sm"></span>
          <span className="ml-2 text-base text-gray-700 dark:text-gray-300 font-medium">
            {tab}
          </span>
          <div
            ref={missingTooltipRef}
            className={`
                absolute z-10 pointer-events-none transition bg-gray-800 text-white text-lg rounded py-1 px-3 shadow-lg
                ${missingTooltipPlacement === "top"
                ? "bottom-full mb-2 left-1/2 -translate-x-1/2"
                : ""
              }
                ${missingTooltipPlacement === "bottom"
                ? "top-full mt-2 left-1/2 -translate-x-1/2"
                : ""
              }
                ${missingTooltipPlacement === "left"
                ? "right-full mr-2 top-1/2 -translate-y-1/2"
                : ""
              }
                ${missingTooltipPlacement === "right"
                ? "left-full ml-2 top-1/2 -translate-y-1/2"
                : ""
              }
                ${missingTooltipOpen
                ? "visible opacity-100"
                : "invisible opacity-0"
              }
                group-hover:visible group-hover:opacity-100
              `}
            style={{
              width: "max-content",
              maxWidth: "50vw",
              whiteSpace: "normal",
              pointerEvents: "auto",
            }}
          >
            {getBeforeApplyTooltipText(tab)}
          </div>
        </div>
        {/* Changed Value Legend */}
        <div
          className="relative group flex items-center cursor-pointer"
          onClick={() => setChangedTooltipOpen((v) => !v)}
          onMouseLeave={() => setChangedTooltipOpen(false)}
        >
          <span className="inline-block w-5 h-5 rounded bg-green-200 dark:bg-green-800 border border-green-400 dark:border-green-700 shadow-sm"></span>
          <span className="ml-2 text-base text-gray-700 dark:text-gray-300 font-medium">
            Changed Value
          </span>
          <div
            ref={changedTooltipRef}
            className={`
                absolute z-10 pointer-events-none transition bg-gray-800 text-white text-lg rounded py-1 px-3 shadow-lg
                ${changedTooltipPlacement === "top"
                ? "bottom-full mb-2 left-1/2 -translate-x-1/2"
                : ""
              }
                ${changedTooltipPlacement === "bottom"
                ? "top-full mt-2 left-1/2 -translate-x-1/2"
                : ""
              }
                ${changedTooltipPlacement === "left"
                ? "right-full mr-2 top-1/2 -translate-y-1/2"
                : ""
              }
                ${changedTooltipPlacement === "right"
                ? "left-full ml-2 top-1/2 -translate-y-1/2"
                : ""
              }
                ${changedTooltipOpen
                ? "visible opacity-100"
                : "invisible opacity-0"
              }
                group-hover:visible group-hover:opacity-100
              `}
            style={{
              width: "max-content",
              maxWidth: "50vw",
              whiteSpace: "normal",
              pointerEvents: "auto",
            }}
          >
            {getAfterApplyTooltipText(tab)}
          </div>
        </div>
      </div>

      {/* Export to CSV */}
      <div className="mt-8 flex justify-end">
        <CSVLink
          data={cleansingDisplayedRows.map(({ clean }, idx) => ({
            Row: idx + 1,
            ...clean,
          }))}
          filename="data_preview.csv"
          className="px-6 py-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 hover:from-indigo-600 hover:to-pink-600 text-white font-bold rounded-lg shadow transition"
        >
          Export CSV
        </CSVLink>
      </div>
    </div>
  );
};

export default ApplyTable;
