from flask import Blueprint, request, jsonify
import pandas as pd
from db import init_db
from bson import ObjectId
from bson.errors import InvalidId
import re
from symspellpy import SymSpell, Verbosity
from rapidfuzz import fuzz
import os

typo2_bp = Blueprint('typo2', __name__)

# ------------ MongoDB ------------ #
from flask import Flask
app = Flask(__name__)
mongo = init_db(app)

# ------------ SymSpell Setup ------------ #
current_path = os.path.dirname(os.path.abspath(__file__))
dictionary_path = os.path.join(current_path, "..", "..", "resources", "frequency_dictionary.txt")
sym_spell = SymSpell(max_dictionary_edit_distance=2, prefix_length=7)
sym_spell.load_dictionary(dictionary_path, term_index=0, count_index=1)

# ------------ Helper: J<PERSON>card ------------ #
def jaccard_similarity(str1, str2):
    set1, set2 = set(str1), set(str2)
    if not set1 or not set2:
        return 0
    return len(set1 & set2) / len(set1 | set2)

# ------------ Hybrid Correction ------------ #
def hybrid_correct(term):
    term = str(term).strip().lower()

    if re.fullmatch(r'\d+', term): return term # ตัวเลขล้วน
    if re.fullmatch(r'[a-zA-Z]{1,2}', term): return term # คำสั้นเกิน เช่น a, in, is
    if re.fullmatch(r'\w+@\w+\.\w+', term): return term  # email address
    if not re.fullmatch(r'[a-zA-Z]+', term): return term # ถ้ามีสัญลักษณ์หรือเลข

    suggestions = sym_spell.lookup(term, Verbosity.CLOSEST, max_edit_distance=2)
    if suggestions:
        sym_term = suggestions[0].term
        sym_score = 1 - (suggestions[0].distance / max(len(term), len(sym_term)))
    else:
        sym_term = term
        sym_score = 0

    lev_score = fuzz.ratio(term, sym_term) / 100
    jaccard_score = jaccard_similarity(term, sym_term)
    final_score = (0.5 * sym_score + 0.3 * lev_score + 0.2 * jaccard_score)

    if final_score > 0.75 and sym_term != term:
        return sym_term
    else:
        return term
    
# ------------ Route: Suggest Typos (all columns) ------------ #
@typo2_bp.route('/typo_suggestions', methods=['POST'])
def typo_suggestion():
    data = request.get_json()
    dataset_id = data.get("dataset_id")

    if not dataset_id:
        return jsonify({"msg": "dataset_id is required"}), 400

    try:
        object_id = ObjectId(dataset_id)
    except Exception as e:
        return jsonify({"msg": f"Invalid dataset_id: {str(e)}"}), 400

    # ดึงข้อมูลจาก MongoDB load current ถ้าไม่มีใช้ previous
    current_doc = mongo.db.current.find_one({"dataset_id": ObjectId(dataset_id)})
    if current_doc:
        df = pd.DataFrame(current_doc["data"])
        previous_data = current_doc["data"]
    else:
        previous_doc = mongo.db.previous.find_one({"dataset_id": ObjectId(dataset_id)})
        if not previous_doc:
            return jsonify({"msg": "Dataset not found"}), 404
        df = pd.DataFrame(previous_doc["data"])
        previous_data = previous_doc["data"]

    suggestions = []

    for col in df.columns:
        if df[col].dtype == object:
            unique_vals = df[col].dropna().unique()
            for val in unique_vals:
                original = str(val)
                corrected = hybrid_correct(original)
                if original != corrected:
                    suggestions.append({
                        "column": col,
                        "original": original,
                        "suggested": corrected
                    })

    # บันทึกข้อมูลที่ผ่านการประมวลผลไปยัง MongoDB
    result_data = df

    # Save previous data
    mongo.db.previous.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": previous_data}},
        upsert=True
    )

    # Update current with cleaned data
    mongo.db.current.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {
            "data": previous_data, 
            "suggestions": suggestions
        }},
        upsert=True
    )

    return jsonify({
        "message": "Suggestions generated successfully",
        "suggestions": suggestions
    }), 200