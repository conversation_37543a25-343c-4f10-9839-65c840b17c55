import React from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  resetFeatures,
  moveFeature,
} from "../../../../app/features/progress/upload/uploadSlice";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import toast, { Toaster } from "react-hot-toast"; // <-- Add this import
// ไลบรารีสำหรับแสดง notification (toast message) ที่ชื่อว่า react-hot-toast 
// เป็นไลบรารี React ยอดนิยมที่ใช้ในการแสดงข้อความ popup แบบรวดเร็วที่มุมหน้าจอ

/**
 * FeatureClassification component for classifying dataset features into
 * numerical, nominal, ordinal, or unassigned categories using drag-and-drop.
 *
 * - Allows users to drag features between categories.
 * - Prevents non-numeric features from being dropped into the numerical category.
 * - Provides reset and start classification controls.
 * - Uses Redux for state management.
 *
 * @component
 * @returns {JSX.Element}
 */
export default function FeatureClassification() {
  const dispatch = useDispatch();
  /**
  * Features object from Redux, containing arrays for each category.
  * @type {import('../../../../app/features/progress/upload/uploadSlice').FeaturesState}
  */
  const features = useSelector((state) => state.upload.features);
  /**
  * The uploaded dataset, used for type checking features.
  * @type {Array<Object>}
  */
  const dataset = useSelector((state) => state.upload.dataset); // <-- Get dataset for type checking
  /**
  * Controls whether the unassigned features panel is shown.
  * @type {[boolean, Function]}
  */
  const [showUnassigned, setShowUnassigned] = React.useState(true);

  /**
   * Checks if a feature is numeric in the dataset.
   * @param {string} featureName - The name of the feature to check.
   * @returns {boolean} True if the feature is mostly numeric, false otherwise.
   */
  // Helper to check if a feature is numeric in the dataset
  const isFeatureNumeric = (featureName) => {
    if (!dataset || dataset.length === 0) return false;
    const values = dataset.map((row) => row[featureName]);
    const nonMissing = values.filter((v) => v !== null && v !== undefined);
    if (nonMissing.length === 0) return false; // All values missing
    const numericCount = nonMissing.filter(
      (v) => typeof v === "number" && !isNaN(v)
    ).length;
    return numericCount / nonMissing.length >= 0.04;
  };

  /**
  * Checks if a feature is mostly string in the dataset.
  * @param {string} featureName - The name of the feature to check.
  * @returns {boolean} True if the feature is mostly string, false otherwise.
  */
  // Helper to check if a feature is mostly string in the dataset
  const isFeatureString = (featureName) => {
    if (!dataset || dataset.length === 0) return false;
    const values = dataset.map((row) => row[featureName]);
    const nonMissing = values.filter((v) => v !== null && v !== undefined);
    if (nonMissing.length === 0) return false; // All values missing
    const stringCount = nonMissing.filter((v) => typeof v === "string").length;
    return stringCount / nonMissing.length >= 0.04;
  };

  /**
  * Handles the drag-and-drop logic for moving features between categories.
  * Prevents invalid moves (e.g., non-numeric to numerical).
  * @param {import("@hello-pangea/dnd").DropResult} result - The drag result object.
  */
  const onDragEnd = (result) => {
    const { source, destination } = result; // แยกตำแหน่งต้นทางและปลายทางที่ถูกลาก
    if (!destination) return; // ถ้าไม่ได้ลากจริง ๆ หรือวางไว้ตำแหน่งเดิม ให้ return ออก
    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    )
      return;

    // Find the dragged item
    let draggedItem = null;
    if (source.droppableId === "unassigned") { // หา item ที่ถูกลากออกมา (เฉพาะที่ไม่ถูก disable)
      const unassignedItems = features.unassigned.items;
      const enabledItems = unassignedItems.filter((item) => !item.disabled);
      draggedItem = enabledItems[source.index];
    } else {
      draggedItem = features[source.droppableId].items.filter(
        (item) => !item.disabled
      )[source.index];
    }

    // If dropping into numerical, check type
    if (
      destination.droppableId === "numerical" &&
      draggedItem &&
      !isFeatureNumeric(draggedItem.feature)
    ) {
      toast.error(
        "Only features with numeric values can be added to Numerical Features. Or your feature have too many missing values.",
        {
          style: {
            borderRadius: "8px",
            background: "#fff",
            color: "#333",
            fontWeight: "bold",
            fontSize: "1.1rem",
            boxShadow: "0 4px 24px rgba(0,0,0,0.10)",
          },
          iconTheme: {
            primary: "#2563eb",
            secondary: "#fff",
          },
        }
      );
      return;
    }

    // Fix for unassigned: map filtered index to real index
    // เนื่องจากบางรายการถูก disable ต้องแปลง index จาก filtered view → actual index
    let realSourceIndex = source.index;
    let realDestinationIndex = destination.index;

    if (source.droppableId === "unassigned") {
      const unassignedItems = features.unassigned.items;
      const enabledItems = unassignedItems.filter((item) => !item.disabled);
      const draggedItem = enabledItems[source.index];
      realSourceIndex = unassignedItems.findIndex(
        (item) => item.id === draggedItem.id
      );
    }
    if (destination.droppableId === "unassigned") {
      const unassignedItems = features.unassigned.items;
      const enabledItems = unassignedItems.filter((item) => !item.disabled);
      if (enabledItems.length === 0) {
        realDestinationIndex = 0;
      } else if (destination.index >= enabledItems.length) {
        realDestinationIndex = unassignedItems.length;
      } else {
        const destItem = enabledItems[destination.index];
        realDestinationIndex = unassignedItems.findIndex(
          (item) => item.id === destItem.id
        );
      }
    }

    dispatch(
      moveFeature({
        source: {
          droppableId: source.droppableId,
          index: realSourceIndex,
        },
        destination: {
          droppableId: destination.droppableId,
          index: realDestinationIndex,
        },
      })
    );
  };

  /**
  * Resets all feature classifications to their initial state.
  */
  const resetFeatureHandler = () => {
    dispatch(resetFeatures());
    setShowUnassigned(true);
  };

  /**
  * Starts the classification process by hiding the unassigned panel.
  */
  const startClassification = () => {
    setShowUnassigned(false);
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <Toaster position="top-center" reverseOrder={false} /> {/* Add Toaster แสดงตำแหน่งที่ toast message จะปรากฏ */}
      <h1 className="text-2xl font-bold mb-6">Feature Classification</h1>
      <div className="flex justify-between mb-4">
        <button
          onClick={resetFeatureHandler}
          className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100"
        >
          Reset
        </button>
        {showUnassigned && (
          <button
            onClick={startClassification}
            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
          >
            Start Classification
          </button>
        )}
      </div>
      <DragDropContext onDragEnd={onDragEnd}> {/* ครอบ layout สำหรับใช้งาน drag-and-drop */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {showUnassigned && (
            <div className="md:col-span-3 mb-4">
              <Droppable droppableId="unassigned" direction="horizontal">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="bg-gray-100 p-4 rounded-lg min-h-[100px] flex flex-wrap gap-2"
                  >
                    <div className="w-full mb-2">
                      <h2 className="font-semibold text-lg">
                        {features.unassigned.title}
                      </h2>
                    </div>
                    {features.unassigned.items
                      .filter((item) => !item.disabled)
                      .map((item, index) => (
                        <Draggable
                          key={item.id}
                          draggableId={item.id}
                          index={index}
                        >
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className="bg-white rounded-full px-4 py-2 flex items-center shadow-sm"
                            >
                              <div className="h-5 w-5 bg-green-500 rounded-full mr-2"></div>
                              <span className="font-medium">
                                {item.feature}
                              </span>
                            </div>
                          )}
                        </Draggable>
                      ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </div>
          )}
          {/* Numerical Features */}
          <div className="border rounded-lg overflow-hidden">
            <Droppable droppableId="numerical">
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="bg-gray-200 rounded-lg p-4 min-h-[400px]"
                >
                  <h2 className="font-bold text-center text-lg mb-4">
                    {features.numerical.title}
                  </h2>
                  {features.numerical.items
                    .filter((item) => !item.disabled)
                    .map((item, index) => (
                      <Draggable
                        key={item.id}
                        draggableId={item.id}
                        index={index}
                      >
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="bg-white rounded-full px-4 py-2 mb-2 flex items-center shadow-sm"
                          >
                            <div className="h-5 w-5 bg-green-500 rounded-full mr-2"></div>
                            <span className="font-medium">{item.feature}</span>
                          </div>
                        )}
                      </Draggable>
                    ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </div>

          {/* Nominal Features */}
          <div className="border rounded-lg overflow-hidden">
            <Droppable droppableId="nominal">
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="bg-gray-200 rounded-lg p-4 min-h-[400px]"
                >
                  <h2 className="font-bold text-center text-lg mb-4">
                    {features.nominal.title}
                  </h2>
                  {features.nominal.items
                    .filter((item) => !item.disabled)
                    .map((item, index) => (
                      <Draggable
                        key={item.id}
                        draggableId={item.id}
                        index={index}
                      >
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="bg-white rounded-full px-4 py-2 mb-2 flex items-center shadow-sm"
                          >
                            <div className="h-5 w-5 bg-green-500 rounded-full mr-2"></div>
                            <span className="font-medium">{item.feature}</span>
                          </div>
                        )}
                      </Draggable>
                    ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </div>

          {/* Ordinal Features */}
          <div className="border rounded-lg overflow-hidden">
            <Droppable droppableId="ordinal">
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="bg-gray-200 rounded-lg p-4 min-h-[400px]"
                >
                  <h2 className="font-bold text-center text-lg mb-4">
                    {features.ordinal.title}
                  </h2>
                  {Array.from(
                    new Set(features.ordinal.items.map((item) => item.id))
                  )
                    .map((id) =>
                      features.ordinal.items.find((i) => i.id === id)
                    )
                    .filter((item) => item && !item.disabled)
                    .map((item, index) => (
                      <Draggable
                        key={item.id}
                        draggableId={item.id}
                        index={index}
                      >
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="bg-white rounded-full px-4 py-2 mb-2 flex items-center shadow-sm"
                          >
                            <div className="h-5 w-5 bg-green-500 rounded-full mr-2"></div>
                            <span className="font-medium">{item.feature}</span>
                          </div>
                        )}
                      </Draggable>
                    ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </div>
        </div>
      </DragDropContext>
    </div>
  );
}
