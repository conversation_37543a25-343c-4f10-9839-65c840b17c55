{"version": 3, "sources": ["../../react-csv/src/components/Download.js", "../../react-csv/src/core.js", "../../react-csv/src/metaProps.js", "../../react-csv/src/components/Link.jsx", "../../react-csv/src/index.js"], "sourcesContent": ["import React from 'react';\nimport {buildURI} from '../core';\nimport {\n   defaultProps as commonDefaultProps,\n   propTypes as commonPropTypes} from '../metaProps';\nconst defaultProps = {\n  target: '_blank'\n};\n\n/**\n *\n * @example ../../sample-site/csvdownload.example.md\n */\nclass CSVDownload extends React.Component {\n\n  static defaultProps = Object.assign(\n    commonDefaultProps,\n    defaultProps\n  );\n\n  static propTypes = commonPropTypes;\n\n  constructor(props) {\n    super(props);\n    this.state={};\n  }\n\n  buildURI() {\n    return buildURI(...arguments);\n  }\n\n  componentDidMount(){\n    const {data, headers, separator, enclosingCharacter, uFEFF, target, specs, replace} = this.props;\n    this.state.page = window.open(\n        this.buildURI(data, uFEFF, headers, separator, enclosingCharacter), target, specs, replace\n    );\n  }\n\n  getWindow() {\n    return this.state.page;\n  }\n\n  render(){\n    return (null)\n  }\n}\n\nexport default CSVDownload;\n", "/**\n * Simple safari detection based on user agent test\n */\nexport const isSafari = () => /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n\nexport const isJsons = ((array) => Array.isArray(array) && array.every(\n  row => (typeof row === 'object' && !(row instanceof Array))\n));\n\nexport const isArrays = ((array) => Array.isArray(array) && array.every(\n  row => Array.isArray(row)\n));\n\nexport const jsonsHeaders = ((array) => Array.from(\n  array.map(json => Object.keys(json))\n    .reduce((a, b) => new Set([...a, ...b]), [])\n));\n\nexport const jsons2arrays = (jsons, headers) => {\n  headers = headers || jsonsHeaders(jsons);\n\n  // allow headers to have custom labels, defaulting to having the header data key be the label\n  let headerLabels = headers;\n  let headerKeys = headers;\n  if (isJsons(headers)) {\n    headerLabels = headers.map((header) => header.label);\n    headerKeys = headers.map((header) => header.key);\n  }\n\n  const data = jsons.map((object) => headerKeys.map((header) => getHeaderValue(header, object)));\n  return [headerLabels, ...data];\n};\n\nexport const getHeaderValue = (property, obj) => {\n  const foundValue = property\n    .replace(/\\[([^\\]]+)]/g, \".$1\")\n    .split(\".\")\n    .reduce(function (o, p, i, arr) {\n      // if at any point the nested keys passed do not exist, splice the array so it doesnt keep reducing\n      const value = o[p];\n      if (value === undefined || value === null) {\n        arr.splice(1);\n      } else {\n        return value;\n      }\n    }, obj);\n  // if at any point the nested keys passed do not exist then looks for key `property` in object obj\n  return (foundValue === undefined) ? ((property in obj) ? obj[property] : '') : foundValue;\n}\n\nexport const elementOrEmpty = (element) =>\n  (typeof element === 'undefined' || element === null) ? '' : element;\n\nexport const joiner = ((data, separator = ',', enclosingCharacter = '\"') => {\n  return data\n    .filter(e => e)\n    .map(\n      row => row\n        .map((element) => elementOrEmpty(element))\n        .map(column => `${enclosingCharacter}${column}${enclosingCharacter}`)\n        .join(separator)\n    )\n    .join(`\\n`);\n});\n\nexport const arrays2csv = ((data, headers, separator, enclosingCharacter) =>\n  joiner(headers ? [headers, ...data] : data, separator, enclosingCharacter)\n);\n\nexport const jsons2csv = ((data, headers, separator, enclosingCharacter) =>\n  joiner(jsons2arrays(data, headers), separator, enclosingCharacter)\n);\n\nexport const string2csv = ((data, headers, separator, enclosingCharacter) =>\n  (headers) ? `${headers.join(separator)}\\n${data}` : data.replace(/\"/g, '\"\"')\n);\n\nexport const toCSV = (data, headers, separator, enclosingCharacter) => {\n  if (isJsons(data)) return jsons2csv(data, headers, separator, enclosingCharacter);\n  if (isArrays(data)) return arrays2csv(data, headers, separator, enclosingCharacter);\n  if (typeof data === 'string') return string2csv(data, headers, separator);\n  throw new TypeError(`Data should be a \"String\", \"Array of arrays\" OR \"Array of objects\" `);\n};\n\nexport const buildURI = ((data, uFEFF, headers, separator, enclosingCharacter) => {\n  const csv = toCSV(data, headers, separator, enclosingCharacter);\n  const type = isSafari() ? 'application/csv' : 'text/csv';\n  const blob = new Blob([uFEFF ? '\\uFEFF' : '', csv], { type });\n  const dataURI = `data:${type};charset=utf-8,${uFEFF ? '\\uFEFF' : ''}${csv}`;\n\n  const URL = window.URL || window.webkitURL;\n\n  return (typeof URL.createObjectURL === 'undefined')\n    ? dataURI\n    : URL.createObjectURL(blob);\n});\n", "import React from 'react';\nimport { string, array, oneOfType, bool, func } from 'prop-types';\n\n\nexport const propTypes = {\n  data: oneOfType([string, array, func]).isRequired,\n  headers: array,\n  target: string,\n  separator: string,\n  filename: string,\n  uFEFF: bool,\n  onClick: func,\n  asyncOnClick: bool,\n  enclosingCharacter: string\n};\n\nexport const defaultProps = {\n  separator: ',',\n  filename: 'generatedBy_react-csv.csv',\n  uFEFF: true,\n  asyncOnClick: false,\n  enclosing<PERSON>haracter: '\"'\n};\n\nexport const PropsNotForwarded = [\n  `data`,\n  `headers`\n];\n", "import React from 'react';\nimport { buildURI, toCSV } from '../core';\nimport {\n  defaultProps as commonDefaultProps,\n  propTypes as commonPropTypes\n} from '../metaProps';\n\n/**\n *\n * @example ../../sample-site/csvlink.example.md\n */\nclass CSVLink extends React.Component {\n  static defaultProps = commonDefaultProps;\n  static propTypes = commonPropTypes;\n\n  constructor(props) {\n    super(props);\n    this.buildURI = this.buildURI.bind(this);\n  }\n\n  buildURI() {\n    return buildURI(...arguments);\n  }\n\n  /**\n   * In IE11 this method will trigger the file download\n   */\n  handleLegacy(event, isAsync = false) {\n    // If this browser is IE 11, it does not support the `download` attribute\n    if (window.navigator.msSaveOrOpenBlob) {\n      // Stop the click propagation\n      event.preventDefault();\n\n      const {\n        data,\n        headers,\n        separator,\n        filename,\n        enclosingCharacter,\n        uFEFF\n      } = this.props;\n\n      const csvData = isAsync && typeof data === 'function' ? data() : data;\n\n      let blob = new Blob([uFEFF ? '\\uFEFF' : '', toCSV(csvData, headers, separator, enclosingCharacter)]);\n      window.navigator.msSaveBlob(blob, filename);\n\n      return false;\n    }\n  }\n\n  handleAsyncClick(event) {\n    const done = proceed => {\n      if (proceed === false) {\n        event.preventDefault();\n        return;\n      }\n      this.handleLegacy(event, true);\n    };\n\n    this.props.onClick(event, done);\n  }\n\n  handleSyncClick(event) {\n    const stopEvent = this.props.onClick(event) === false;\n    if (stopEvent) {\n      event.preventDefault();\n      return;\n    }\n    this.handleLegacy(event);\n  }\n\n  handleClick() {\n    return event => {\n      if (typeof this.props.onClick === 'function') {\n        return this.props.asyncOnClick\n          ? this.handleAsyncClick(event)\n          : this.handleSyncClick(event);\n      }\n      this.handleLegacy(event);\n    };\n  }\n\n  render() {\n    const {\n      data,\n      headers,\n      separator,\n      filename,\n      uFEFF,\n      children,\n      onClick,\n      asyncOnClick,\n      enclosingCharacter,\n      ...rest\n    } = this.props;\n\n    const isNodeEnvironment = typeof window === 'undefined';\n    const href = isNodeEnvironment ? '' : this.buildURI(data, uFEFF, headers, separator, enclosingCharacter)\n\n    return (\n      <a\n        download={filename}\n        {...rest}\n        ref={link => (this.link = link)}\n        target=\"_self\"\n        href={href}\n        onClick={this.handleClick()}\n      >\n        {children}\n      </a>\n    );\n  }\n}\n\nexport default CSVLink;\n", "import Download from './components/Download';\nimport Link from './components/Link';\n\nexport const CSVDownload = Download;\nexport const CSVLink = Link;\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,gBAAkB;;;ACGX,IAAM,WAAW,MAAM,iCAAiC,KAAK,UAAU,SAAS;AAEhF,IAAM,UAAW,CAACC,WAAU,MAAM,QAAQA,MAAK,KAAKA,OAAM;AAAA,EAC/D,SAAQ,OAAO,QAAQ,YAAY,EAAE,eAAe;AACtD;AAEO,IAAM,WAAY,CAACA,WAAU,MAAM,QAAQA,MAAK,KAAKA,OAAM;AAAA,EAChE,SAAO,MAAM,QAAQ,GAAG;AAC1B;AAEO,IAAM,eAAgB,CAACA,WAAU,MAAM;AAAA,EAC5CA,OAAM,IAAI,UAAQ,OAAO,KAAK,IAAI,CAAC,EAChC,OAAO,CAAC,GAAG,MAAM,oBAAI,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC/C;AAEO,IAAM,eAAe,CAAC,OAAO,YAAY;AAC9C,YAAU,WAAW,aAAa,KAAK;AAGvC,MAAI,eAAe;AACnB,MAAI,aAAa;AACjB,MAAI,QAAQ,OAAO,GAAG;AACpB,mBAAe,QAAQ,IAAI,CAAC,WAAW,OAAO,KAAK;AACnD,iBAAa,QAAQ,IAAI,CAAC,WAAW,OAAO,GAAG;AAAA,EACjD;AAEA,QAAM,OAAO,MAAM,IAAI,CAAC,WAAW,WAAW,IAAI,CAAC,WAAW,eAAe,QAAQ,MAAM,CAAC,CAAC;AAC7F,SAAO,CAAC,cAAc,GAAG,IAAI;AAC/B;AAEO,IAAM,iBAAiB,CAAC,UAAU,QAAQ;AAC/C,QAAM,aAAa,SAChB,QAAQ,gBAAgB,KAAK,EAC7B,MAAM,GAAG,EACT,OAAO,SAAU,GAAG,GAAG,GAAG,KAAK;AAE9B,UAAM,QAAQ,EAAE,CAAC;AACjB,QAAI,UAAU,UAAa,UAAU,MAAM;AACzC,UAAI,OAAO,CAAC;AAAA,IACd,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,GAAG,GAAG;AAER,SAAQ,eAAe,SAAe,YAAY,MAAO,IAAI,QAAQ,IAAI,KAAM;AACjF;AAEO,IAAM,iBAAiB,CAAC,YAC5B,OAAO,YAAY,eAAe,YAAY,OAAQ,KAAK;AAEvD,IAAM,SAAU,CAAC,MAAM,YAAY,KAAK,qBAAqB,QAAQ;AAC1E,SAAO,KACJ,OAAO,OAAK,CAAC,EACb;AAAA,IACC,SAAO,IACJ,IAAI,CAAC,YAAY,eAAe,OAAO,CAAC,EACxC,IAAI,YAAU,GAAG,kBAAkB,GAAG,MAAM,GAAG,kBAAkB,EAAE,EACnE,KAAK,SAAS;AAAA,EACnB,EACC,KAAK;AAAA,CAAI;AACd;AAEO,IAAM,aAAc,CAAC,MAAM,SAAS,WAAW,uBACpD,OAAO,UAAU,CAAC,SAAS,GAAG,IAAI,IAAI,MAAM,WAAW,kBAAkB;AAGpE,IAAM,YAAa,CAAC,MAAM,SAAS,WAAW,uBACnD,OAAO,aAAa,MAAM,OAAO,GAAG,WAAW,kBAAkB;AAG5D,IAAM,aAAc,CAAC,MAAM,SAAS,WAAW,uBACnD,UAAW,GAAG,QAAQ,KAAK,SAAS,CAAC;AAAA,EAAK,IAAI,KAAK,KAAK,QAAQ,MAAM,IAAI;AAGtE,IAAM,QAAQ,CAAC,MAAM,SAAS,WAAW,uBAAuB;AACrE,MAAI,QAAQ,IAAI,EAAG,QAAO,UAAU,MAAM,SAAS,WAAW,kBAAkB;AAChF,MAAI,SAAS,IAAI,EAAG,QAAO,WAAW,MAAM,SAAS,WAAW,kBAAkB;AAClF,MAAI,OAAO,SAAS,SAAU,QAAO,WAAW,MAAM,SAAS,SAAS;AACxE,QAAM,IAAI,UAAU,qEAAqE;AAC3F;AAEO,IAAM,WAAY,CAAC,MAAM,OAAO,SAAS,WAAW,uBAAuB;AAChF,QAAM,MAAM,MAAM,MAAM,SAAS,WAAW,kBAAkB;AAC9D,QAAM,OAAO,SAAS,IAAI,oBAAoB;AAC9C,QAAM,OAAO,IAAI,KAAK,CAAC,QAAQ,WAAW,IAAI,GAAG,GAAG,EAAE,KAAK,CAAC;AAC5D,QAAM,UAAU,QAAQ,IAAI,kBAAkB,QAAQ,WAAW,EAAE,GAAG,GAAG;AAEzE,QAAM,MAAM,OAAO,OAAO,OAAO;AAEjC,SAAQ,OAAO,IAAI,oBAAoB,cACnC,UACA,IAAI,gBAAgB,IAAI;AAC9B;;;AC/FA,mBAAkB;AAClB,wBAAqD;AAG9C,IAAM,YAAY;AAAA,EACvB,UAAM,6BAAU,CAAC,0BAAQ,yBAAO,sBAAI,CAAC,EAAE;AAAA,EACvC,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,EACT,cAAc;AAAA,EACd,oBAAoB;AACtB;AAEO,IAAM,eAAe;AAAA,EAC1B,WAAW;AAAA,EACX,UAAU;AAAA,EACV,OAAO;AAAA,EACP,cAAc;AAAA,EACd,oBAAoB;AACtB;;;AFjBA,IAAMC,gBAAe;AAAA,EACnB,QAAQ;AACV;AAMA,IAAM,cAAN,cAA0B,cAAAC,QAAM,UAAU;AAAA,EASxC,YAAY,OAAO;AACjB,UAAM,KAAK;AACX,SAAK,QAAM,CAAC;AAAA,EACd;AAAA,EAEA,WAAW;AACT,WAAO,SAAS,GAAG,SAAS;AAAA,EAC9B;AAAA,EAEA,oBAAmB;AACjB,UAAM,EAAC,MAAM,SAAS,WAAW,oBAAoB,OAAO,QAAQ,OAAO,QAAO,IAAI,KAAK;AAC3F,SAAK,MAAM,OAAO,OAAO;AAAA,MACrB,KAAK,SAAS,MAAM,OAAO,SAAS,WAAW,kBAAkB;AAAA,MAAG;AAAA,MAAQ;AAAA,MAAO;AAAA,IACvF;AAAA,EACF;AAAA,EAEA,YAAY;AACV,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EAEA,SAAQ;AACN,WAAQ;AAAA,EACV;AACF;AA9BE,cAFI,aAEG,gBAAe,OAAO;AAAA,EAC3B;AAAA,EACAD;AACF;AAEA,cAPI,aAOG,aAAY;AA2BrB,IAAO,mBAAQ;;;AG/Cf,IAAAE,gBAAkB;AAWlB,IAAM,UAAN,cAAsB,cAAAC,QAAM,UAAU;AAAA,EAIpC,YAAY,OAAO;AACjB,UAAM,KAAK;AACX,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AAAA,EACzC;AAAA,EAEA,WAAW;AACT,WAAO,SAAS,GAAG,SAAS;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAO,UAAU,OAAO;AAEnC,QAAI,OAAO,UAAU,kBAAkB;AAErC,YAAM,eAAe;AAErB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AAET,YAAM,UAAU,WAAW,OAAO,SAAS,aAAa,KAAK,IAAI;AAEjE,UAAI,OAAO,IAAI,KAAK,CAAC,QAAQ,WAAW,IAAI,MAAM,SAAS,SAAS,WAAW,kBAAkB,CAAC,CAAC;AACnG,aAAO,UAAU,WAAW,MAAM,QAAQ;AAE1C,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,iBAAiB,OAAO;AACtB,UAAM,OAAO,aAAW;AACtB,UAAI,YAAY,OAAO;AACrB,cAAM,eAAe;AACrB;AAAA,MACF;AACA,WAAK,aAAa,OAAO,IAAI;AAAA,IAC/B;AAEA,SAAK,MAAM,QAAQ,OAAO,IAAI;AAAA,EAChC;AAAA,EAEA,gBAAgB,OAAO;AACrB,UAAM,YAAY,KAAK,MAAM,QAAQ,KAAK,MAAM;AAChD,QAAI,WAAW;AACb,YAAM,eAAe;AACrB;AAAA,IACF;AACA,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EAEA,cAAc;AACZ,WAAO,WAAS;AACd,UAAI,OAAO,KAAK,MAAM,YAAY,YAAY;AAC5C,eAAO,KAAK,MAAM,eACd,KAAK,iBAAiB,KAAK,IAC3B,KAAK,gBAAgB,KAAK;AAAA,MAChC;AACA,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EAEA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,IAAI,KAAK;AAET,UAAM,oBAAoB,OAAO,WAAW;AAC5C,UAAM,OAAO,oBAAoB,KAAK,KAAK,SAAS,MAAM,OAAO,SAAS,WAAW,kBAAkB;AAEvG,WACE,cAAAA,QAAA;AAAA,MAAC;AAAA;AAAA,QACC,UAAU;AAAA,QACT,GAAG;AAAA,QACJ,KAAK,UAAS,KAAK,OAAO;AAAA,QAC1B,QAAO;AAAA,QACP;AAAA,QACA,SAAS,KAAK,YAAY;AAAA;AAAA,MAEzB;AAAA,IACH;AAAA,EAEJ;AACF;AArGE,cADI,SACG,gBAAe;AACtB,cAFI,SAEG,aAAY;AAsGrB,IAAO,eAAQ;;;AChHR,IAAMC,eAAc;AACpB,IAAMC,WAAU;", "names": ["import_react", "array", "defaultProps", "React", "import_react", "React", "CSVDownload", "CSVLink"]}