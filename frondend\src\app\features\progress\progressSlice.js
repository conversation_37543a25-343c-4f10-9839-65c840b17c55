import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  steps: [
    { id: 0, title: "Upload" },
    { id: 1, title: "Cleansing" },
    { id: 2, title: "Visualization" },
    { id: 3, title: "Statistic" },
    { id: 4, title: "Predict" },
  ],
  currentStep: 0,
};

export const progressSlice = createSlice({
  name: "progress",
  initialState,
  reducers: {
    setNextStep: (state) => {
      if (state.currentStep < state.steps.length - 1) {
        state.currentStep += 1; // Correctly mutate the state
      }
    },
    setPrevStep: (state) => {
      if (state.currentStep > 0) {
        state.currentStep -= 1; // Correctly mutate the state
      }
    },
  },
});

export const { setNextStep, setPrevStep } = progressSlice.actions;
export default progressSlice.reducer;
