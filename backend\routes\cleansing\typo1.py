from flask import Blueprint, request, jsonify
import pandas as pd
from db import init_db
from bson import ObjectId
from bson.errors import InvalidId

typo1_bp = Blueprint('typo1', __name__)

# MongoDB
from flask import Flask
app = Flask(__name__)
mongo = init_db(app)

# -------------------- Function TYPO IN COLUMN --------------------
def typo_in_column(dataset_id, column, corrections):
    # ตรวจสอบว่า dataset_id เป็น ObjectId ที่ถูกต้องหรือไม่
    try:
        object_id = ObjectId(dataset_id)
    except InvalidId:
        return None, "Invalid Dataset ID"

    # ดึงข้อมูลจาก MongoDB load current ถ้าไม่มีใช้ previous
    current_doc = mongo.db.current.find_one({"dataset_id": object_id})
    if current_doc:
        df = pd.DataFrame(current_doc["data"])
        previous_data = current_doc["data"]
    else:
        previous_doc = mongo.db.previous.find_one({"dataset_id": object_id})
        if not previous_doc:
            return None, "Dataset not found"
        df = pd.DataFrame(previous_doc["data"])
        previous_data = previous_doc["data"]

    # ตรวจสอบว่าคอลัมน์ที่ผู้ใช้เลือกมีอยู่ใน DataFrame หรือไม่
    if column not in df.columns:
        return None, f"Column '{column}' not found in the dataset"

    # แก้ไขค่าผิดพลาด (typos) ตามที่ผู้ใช้กำหนดใน corrections
    original_data = df[column].copy()

    # ใช้ replace() โดยไม่ต้องแปลงเป็น string ถ้าค่าที่ถูกแทนที่เป็น int
    df[column] = df[column].replace(corrections)

    # ตรวจสอบว่ามีการเปลี่ยนแปลงข้อมูลจริงๆ หรือไม่
    if original_data.equals(df[column]):
        return df.to_dict(orient='records'), "No changes made to the data"

    # บันทึกข้อมูลที่แก้ไขแล้วกลับไปที่ MongoDB
    mongo.db.current.update_one(
        {"dataset_id": object_id},
        {"$set": {"data": df.to_dict(orient='records')}},
        upsert=True
    )

    return df.to_dict(orient='records'), None

# -------------------- ROUTE TYPO IN COLUMN --------------------
@typo1_bp.route('/typo_column', methods=['POST'])
def correct_typo():
    # รับข้อมูลจากผู้ใช้
    data = request.get_json()

    # ดึงค่า column ที่ผู้ใช้เลือกและการแก้ไขที่ต้องการ
    dataset_id = data.get("dataset_id")
    column = data['column']
    corrections = data['corrections']

    if not dataset_id:
        return jsonify({"msg": "Dataset ID is required"}), 400

    # ปรับ corrections
    corrections = {int(k) if k.isdigit() else k: v for k, v in corrections.items()}

    # เรียกใช้ฟังก์ชันเพื่อทำการแก้ไข typo
    corrected_data, error_msg = typo_in_column(dataset_id, column, corrections)

    if error_msg:
        return jsonify({"msg": error_msg}), 400

    # ส่งผลลัพธ์กลับไปที่ผู้ใช้
    return jsonify({
        "message": "Typos corrected successfully",
        "data": corrected_data
    }), 200
