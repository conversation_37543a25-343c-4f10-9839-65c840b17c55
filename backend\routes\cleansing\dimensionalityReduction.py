from flask import Blueprint, request, jsonify
from db import init_db
import pandas as pd
from bson import ObjectId
from sklearn.decomposition import PCA
from umap.umap_ import UMAP

# สร้าง Blueprint สำหรับ duplicate handling
dimensionalityReduction_bp = Blueprint('dimensionalityReduction', __name__)

# MongoDB
from flask import Flask
app = Flask(__name__)
mongo = init_db(app)

# -------- PCA Function (with Whitening) --------
def apply_pca(df, columns=None, n_components=2, whiten=False):

    # ถ้ามีการเลือกคอลัมน์ จะเลือกเฉพาะคอลัมน์ที่ผู้ใช้ระบุ
    if columns:
        df = df[columns]

    # เลือกเฉพาะข้อมูลที่เป็นตัวเลขและลบค่า NaN
    numeric_df = df.select_dtypes(include=["number"]).dropna()
    if numeric_df.empty:
        raise ValueError("No numeric columns available for PCA")
    
    # สร้างและฝึกโมเดล PCA
    pca = PCA(n_components=n_components, whiten=whiten)
    reduced = pca.fit_transform(numeric_df) # ลดคอลัมน์ของข้อมูล
    cols = [f"PC{i+1}" for i in range(n_components)] # สร้างชื่อคอลัมน์ใหม่
    return pd.DataFrame(reduced, columns=cols) # คืนค่าผลลัพธ์เป็น DataFrame

# -------- UMAP Function --------
def apply_umap(df, columns=None, n_components=2):

    # ถ้ามีการเลือกคอลัมน์ จะเลือกเฉพาะคอลัมน์ที่ผู้ใช้ระบุ
    if columns:
        df = df[columns]

    # เลือกเฉพาะข้อมูลที่เป็นตัวเลขและลบค่า NaN
    numeric_df = df.select_dtypes(include=["number"]).dropna()
    if numeric_df.empty:
        raise ValueError("No numeric columns available for UMAP")
    
    # สร้างและฝึกโมเดล UMAP
    reducer = UMAP(n_components=n_components) 
    reduced = reducer.fit_transform(numeric_df) # ลดคอลัมน์ของข้อมูล
    cols = [f"UMAP{i+1}" for i in range(n_components)] # สร้างชื่อคอลัมน์ใหม่
    return pd.DataFrame(reduced, columns=cols) # คืนค่าผลลัพธ์เป็น DataFrame

# -------- PCA Route --------
@dimensionalityReduction_bp.route("/reduction/pca", methods=["POST"])
def reduce_pca():
    data = request.json
    dataset_id = data.get("dataset_id")
    columns = data.get("columns")  # คอลัมน์ที่ผู้ใช้เลือก
    n_components = data.get("n_components", 2) # จำนวนคอลัมน์ที่ต้องการลด
    whiten = data.get("whiten", False) # การเลือกให้ทำ Whitening หรือไม่

    # ตรวจสอบว่า dataset_id ถูกส่งมาหรือไม่
    if not dataset_id:
        return jsonify({"msg": "dataset_id is required"}), 400
    
    # โหลดข้อมูลจาก MongoDB
    current_doc = mongo.db.current.find_one({"dataset_id": ObjectId(dataset_id)})
    if current_doc:
        df = pd.DataFrame(current_doc["data"])
        previous_data = current_doc["data"]
    else:
        previous_doc = mongo.db.previous.find_one({"dataset_id": ObjectId(dataset_id)})
        if not previous_doc:
            return jsonify({"msg": "Dataset not found"}), 404
        df = pd.DataFrame(previous_doc["data"])
        previous_data = previous_doc["data"]

    try:
        # เรียกใช้ฟังก์ชัน apply_pca
        reduced_df = apply_pca(df, columns=columns, n_components=n_components, whiten=whiten)
    except Exception as e:
        return jsonify({"msg": str(e)}), 500
    
    # อัปเดตข้อมูลใน MongoDB
    mongo.db.previous.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": previous_data}},
        upsert=True
    )

    mongo.db.current.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": reduced_df.to_dict(orient="records")}},
        upsert=True
    )

    return jsonify({
        "msg": f"PCA completed with {'whitening' if whiten else 'no whitening'} and saved",
        "pca_result": reduced_df.to_dict(orient="records")
    }), 200

# -------- UMAP Route --------
@dimensionalityReduction_bp.route("/reduction/umap", methods=["POST"])
def reduce_umap():
    data = request.json
    dataset_id = data.get("dataset_id")
    columns = data.get("columns")  # คอลัมน์ที่ผู้ใช้เลือก
    n_components = data.get("n_components", 2) # จำนวนคอลัมน์ที่ต้องการลด

    # ตรวจสอบว่า dataset_id ถูกส่งมาหรือไม่
    if not dataset_id:
        return jsonify({"msg": "dataset_id is required"}), 400
    
    # โหลดข้อมูลจาก MongoDB
    current_doc = mongo.db.current.find_one({"dataset_id": ObjectId(dataset_id)})
    if current_doc:
        df = pd.DataFrame(current_doc["data"])
        previous_data = current_doc["data"]
    else:
        previous_doc = mongo.db.previous.find_one({"dataset_id": ObjectId(dataset_id)})
        if not previous_doc:
            return jsonify({"msg": "Dataset not found"}), 404
        df = pd.DataFrame(previous_doc["data"])
        previous_data = previous_doc["data"]

    try:
        # เรียกใช้ฟังก์ชัน apply_umap
        reduced_df = apply_umap(df, columns=columns, n_components=n_components)
    except Exception as e:
        return jsonify({"msg": str(e)}), 500
    
    # อัปเดตข้อมูลใน MongoDB
    mongo.db.previous.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": previous_data}},
        upsert=True
    )

    mongo.db.current.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": reduced_df.to_dict(orient="records")}},
        upsert=True
    )

    return jsonify({
        "msg": "UMAP completed and saved",
        "umap_result": reduced_df.to_dict(orient="records")
    }), 200