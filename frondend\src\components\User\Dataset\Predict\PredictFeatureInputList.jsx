import React from "react";
/**
 * PredictFeatureInputList is a UI component that renders input controls
 * (either number input or dropdown) for each feature used in prediction.
 * It dynamically renders based on feature metadata (type, unique values, etc.)
 * and excludes the target column from being selectable.
 *
 * This component is commonly used in supervised learning workflows to allow
 * users to enter or select input values for prediction.
 *
 * @component
 * @param {Object} props - Component props
 * @param {string[]} props.allFeatureOrder - Array of feature IDs indicating the order of features to display
 * @param {Object.<string, Object>} props.allItemsMap - A map of feature ID to feature metadata (e.g., { id, feature, uniqueValue, type })
 * @param {Object.<string, string[]>} props.ordinalMap - A map of feature names to their ordered list of values (used to distinguish ordinal features)
 * @param {string} props.targetColumn - The name of the target column to be excluded from prediction inputs
 * @param {Object.<string, any>} props.selectedFeatureValues - Current selected values for each feature (keyed by feature name)
 * @param {Function} props.setSelectedFeatureValues - Function to update selected feature values (takes previous state and returns new state)
 * @param {Function} props.onTrainClick - Callback fired when the "Run Prediction" button is clicked
 *
 * @returns {JSX.Element} Rendered input form for prediction features
 *
 * @example
 * <PredictFeatureInputList
 *   allFeatureOrder={["1", "2", "3"]}
 *   allItemsMap={{
 *     "1": { id: "1", feature: "age", type: "numerical" },
 *     "2": { id: "2", feature: "gender", uniqueValue: ["Male", "Female"] },
 *     "3": { id: "3", feature: "education", uniqueValue: ["High", "Low"] },
 *   }}
 *   ordinalMap={{ education: ["Low", "High"] }}
 *   targetColumn="income"
 *   selectedFeatureValues={{ age: 30, gender: "Male" }}
 *   setSelectedFeatureValues={setSelectedFeatureValues}
 *   onTrainClick={() => console.log("Training")}
 * />
 */

const PredictFeatureInputList = ({
    allFeatureOrder = [], // Array of feature IDs in the order they should be displayed
    allItemsMap = {}, // Map of feature ID to feature metadata (e.g., { id, feature, uniqueValue, type })
    ordinalMap = {}, // Map of feature names to their ordered list of values (used to distinguish ordinal features)
    targetColumn = "", // The name of the target column to be excluded from prediction inputs
    selectedFeatureValues = {}, // Current selected values for each feature (keyed by feature name)
    setSelectedFeatureValues = () => { }, // Function to update selected feature values (takes previous state and returns new state)
    onTrainClick = () => { }, // Callback fired when the "Run Prediction" button is clicked
}) => {
    return (
        <div>
            <label className="block text-sm font-medium mb-1">Predict:</label>
            <ul className="flex flex-wrap gap-4">
                {allFeatureOrder.map((id) => { // ใช้ .map เพื่อวนลูปแสดง input สำหรับแต่ละ feature ตามลำดับจาก allFeatureOrder
                    const item = allItemsMap[id]; // ถ้าไม่เจอข้อมูลของ feature หรือเป็น targetColumn ให้ข้าม
                    if (!item) return null;
                    if (item.feature === targetColumn) return null;

                    // ดึงค่าที่ผู้ใช้เคยเลือกมาแสดงใน input (หรือเป็นค่าว่างถ้ายังไม่มี)
                    const selectedValue = selectedFeatureValues[item.feature] || "";

                    // ถ้ามี type ใช้เลย
                    let type = item.type || "";

                    // ถ้าไม่มี type ให้เช็ค uniqueValue
                    if (!type) {
                        if (
                            !item.uniqueValue || // ไม่มี uniqueValue
                            item.uniqueValue.length === 0
                        ) {
                            type = "numerical"; // ไม่มี uniqueValue = numerical
                        } else {
                            // ตรวจสอบดูว่า uniqueValue เป็น array ของ string หรือ number
                            // สมมติว่าถ้า first element เป็น number ให้ถือเป็น numerical
                            const firstVal = item.uniqueValue[0];
                            if (typeof firstVal === "number") {
                                type = "numerical";
                            } else {
                                // อื่น ๆ ถือ nominal/ordinal
                                type = ordinalMap[item.feature] ? "ordinal" : "nominal";
                            }
                        }
                    }

                    return (
                        <li key={item.id} className="flex flex-col w-48">
                            <label className="mb-1 font-medium">{item.feature}:</label>

                            {/* ถ้าเป็น numerical ให้ใช้ <input type="number" /> */}
                            {type === "numerical" ? (
                                <input
                                    type="number"
                                    className="border rounded px-2 py-1"
                                    value={selectedValue}
                                    onChange={(e) => {
                                        const newValue = e.target.value;
                                        setSelectedFeatureValues((prev) => ({
                                            ...prev,
                                            [item.feature]: newValue,
                                        }));
                                        console.log(`Selected ${item.feature}: ${newValue}`);
                                    }}
                                />
                            ) : (
                                <select
                                    className="border rounded px-2 py-1"
                                    value={selectedValue}
                                    onChange={(e) => {
                                        const newValue = e.target.value;
                                        setSelectedFeatureValues((prev) => ({
                                            ...prev,
                                            [item.feature]: newValue,
                                        }));
                                        console.log(`Selected ${item.feature}: ${newValue}`);
                                    }}
                                >
                                    {item.uniqueValue.map((val) => (
                                        <option key={val} value={val}>
                                            {val}
                                        </option>
                                    ))}
                                </select>
                            )}
                        </li>
                    );
                })}
            </ul>

            <button
                type="button"
                className="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                onClick={onTrainClick}
            >
                Run Prediction
            </button>
        </div>
    );
};

export default PredictFeatureInputList;
