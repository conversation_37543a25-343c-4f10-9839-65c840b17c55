{"hash": "c6a0f5c8", "configHash": "0db37483", "lockfileHash": "fa5178cc", "browserHash": "d56abb40", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "10a2cc14", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "64cb8414", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "9a6de46a", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "17e464db", "needsInterop": true}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "cbd18c92", "needsInterop": false}, "chart.js/auto": {"src": "../../chart.js/auto/auto.js", "file": "chart__js_auto.js", "fileHash": "668<PERSON><PERSON>dd", "needsInterop": false}, "chartjs-plugin-datalabels": {"src": "../../chartjs-plugin-datalabels/dist/chartjs-plugin-datalabels.esm.js", "file": "chartjs-plugin-datalabels.js", "fileHash": "4c52d15f", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "b022705f", "needsInterop": false}, "density-clustering": {"src": "../../density-clustering/lib/index.js", "file": "density-clustering.js", "fileHash": "3840e5b8", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "5ada6310", "needsInterop": false}, "@hello-pangea/dnd": {"src": "../../@hello-pangea/dnd/dist/dnd.esm.js", "file": "@hello-pangea_dnd.js", "fileHash": "d6524ea9", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "d92b1a5a", "needsInterop": false}, "plotly.js-dist-min": {"src": "../../plotly.js-dist-min/plotly.min.js", "file": "plotly__js-dist-min.js", "fileHash": "43da4bce", "needsInterop": true}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "49c57dfb", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "04584f9d", "needsInterop": false}, "react-csv": {"src": "../../react-csv/src/index.js", "file": "react-csv.js", "fileHash": "3aeaacbc", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "4253914e", "needsInterop": true}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "ed26e9aa", "needsInterop": false}, "react-plotly.js": {"src": "../../react-plotly.js/react-plotly.js", "file": "react-plotly__js.js", "fileHash": "4bec7b97", "needsInterop": true}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "30f1e86e", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "15c59783", "needsInterop": false}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "c5f87549", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "4a006f93", "needsInterop": false}}, "chunks": {"chunk-25BPZU7H": {"file": "chunk-25BPZU7H.js"}, "chunk-RJJ6DPM5": {"file": "chunk-RJJ6DPM5.js"}, "chunk-IEEFXWTP": {"file": "chunk-IEEFXWTP.js"}, "chunk-OUGDY7HK": {"file": "chunk-OUGDY7HK.js"}, "chunk-OX56QJAV": {"file": "chunk-OX56QJAV.js"}, "chunk-5AS4NL4J": {"file": "chunk-5AS4NL4J.js"}, "chunk-RUPNRBO7": {"file": "chunk-RUPNRBO7.js"}, "chunk-OUR6N6YD": {"file": "chunk-OUR6N6YD.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}