import React, { useState, useEffect } from "react";

/**
 * ConstantImputation component allows the user to input constant values to fill missing data.
 * This supports both numerical and categorical features.
 */
export default function ConstantImputation({
  features,
  isNumerical = true,
  placeholder,
  methodIndex,
  numericalMissingValueMethods,
  setNumericalMissingValueMethods,
  categoricalMissingValueMethods,
  setCategoricalMissingValueMethods,
}) {
  const [featureRows, setFeatureRows] = useState([
    { id: "row-" + Date.now(), feature: "", value: "" },
  ]);

  useEffect(() => {
    if (methodIndex !== undefined) {
      const selectedFeatures = featureRows
        .filter((row) => row.feature)
        .map((row) => ({
          feature: row.feature.replace(/-\d+$/, ""),
          value: isNumerical ? Number(row.value) : row.value,
        }));
      const currentFeatures = isNumerical
        ? [...(numericalMissingValueMethods[methodIndex]?.features || [])]
        : [...(categoricalMissingValueMethods[methodIndex]?.features || [])];
      const hasChanged =
        JSON.stringify(currentFeatures) !== JSON.stringify(selectedFeatures);

      if (hasChanged) {
        if (isNumerical) {
          setNumericalMissingValueMethods(
            numericalMissingValueMethods.map((method, idx) =>
              idx === methodIndex ? { ...method, features: selectedFeatures } : method
            )
          );
        } else {
          setCategoricalMissingValueMethods(
            categoricalMissingValueMethods.map((method, idx) =>
              idx === methodIndex ? { ...method, features: selectedFeatures } : method
            )
          );
        }
      }
    }
  }, [
    featureRows,
    setNumericalMissingValueMethods,
    setCategoricalMissingValueMethods,
    methodIndex,
    isNumerical,
  ]);

  useEffect(() => {
    const selectedFeatureIds = featureRows.filter((row) => row.feature).map((row) => row.feature);
    const availableFeatures = features.filter((f) => !selectedFeatureIds.includes(f.id));
    if (availableFeatures.length === 0) {
      const hasEmptyRows = featureRows.some((row) => !row.feature);
      if (hasEmptyRows) {
        setFeatureRows((prev) => prev.filter((row) => row.feature));
      }
    }
  }, [features]);

  const handleFeatureChange = (id, value) => {
    setFeatureRows((prev) =>
      prev.map((row) =>
        row.id === id
          ? { ...row, feature: value, dropdownOpen: false, search: "" }
          : row
      )
    );
  };

  const handleValueChange = (id, value) => {
    setFeatureRows((prev) =>
      prev.map((row) => (row.id === id ? { ...row, value } : row))
    );
  };

  const handleRemoveRow = (id) => {
    setFeatureRows((prev) => prev.filter((row) => row.id !== id));
  };

  const handleAddFeature = () => {
    setFeatureRows((prev) => [
      ...prev,
      { id: "row-" + Date.now(), feature: "", value: "" },
    ]);
  };

  const hasAvailableFeatures =
    features.length > 0 &&
    features.some((f) => !featureRows.some((row) => row.feature === f.id));
  const hasEmptyRows = featureRows.some((row) => !row.feature);

  return (
    <div className="max-w-xl mx-auto bg-white rounded-2xl shadow-lg border border-gray-100 p-6 font-sans">
      <h2 className="text-2xl font-bold mb-6 text-blue-700 flex items-center gap-2">
        <span className="inline-block w-2 h-8 bg-blue-400 rounded-full mr-2"></span>
        Fill Missing Values with Constant
      </h2>
      <div className="space-y-5">
        {featureRows.map((row, index) => {
          const selectedFeatureIds = featureRows
            .filter((r) => r.id !== row.id)
            .map((r) => r.feature);

          return (
            <div key={row.id} className="bg-gray-50 rounded-lg p-4 shadow flex flex-col md:flex-row items-center gap-4 mb-2">
              <div className="relative w-full md:w-1/2">
                <button
                  type="button"
                  onClick={() =>
                    setFeatureRows((prev) =>
                      prev.map((r) =>
                        r.id === row.id
                          ? { ...r, dropdownOpen: !r.dropdownOpen }
                          : { ...r, dropdownOpen: false }
                      )
                    )
                  }
                  className="w-full py-2 px-4 pr-10 rounded-lg border border-gray-300 bg-white text-base cursor-pointer flex justify-between items-center transition focus:ring-2 focus:ring-blue-400"
                >
                  <span>
                    {features.find((f) => f.id === row.feature)?.feature ||
                      "Select feature"}
                  </span>
                  <span
                    className={`ml-2 transition-transform ${
                      row.dropdownOpen ? "rotate-180" : ""
                    }`}
                  >
                    ▼
                  </span>
                </button>
                {row.dropdownOpen && (
                  <div className="absolute z-10 mt-2 w-full bg-white rounded-lg shadow-xl border border-gray-200">
                    <input
                      type="text"
                      className="w-full px-3 py-2 text-sm border-b border-gray-100 focus:outline-none"
                      placeholder="Search feature..."
                      value={row.search || ""}
                      onChange={(e) =>
                        setFeatureRows((prev) =>
                          prev.map((r) =>
                            r.id === row.id
                              ? { ...r, search: e.target.value }
                              : r
                          )
                        )
                      }
                    />
                    <div className="max-h-48 overflow-y-auto">
                      {features
                        .filter(
                          (item) =>
                            !selectedFeatureIds.includes(item.id) &&
                            (!row.search ||
                              item.feature
                                .toLowerCase()
                                .includes(row.search.toLowerCase()))
                        )
                        .map((item) => (
                          <div
                            key={item.id}
                            className="cursor-pointer px-5 py-2 hover:bg-blue-100"
                            onClick={() => {
                              handleFeatureChange(row.id, item.id);
                            }}
                          >
                            {item.feature}
                          </div>
                        ))}
                      {features.filter(
                        (item) =>
                          !selectedFeatureIds.includes(item.id) &&
                          (!row.search ||
                            item.feature
                              .toLowerCase()
                              .includes(row.search.toLowerCase()))
                      ).length === 0 && (
                        <div className="px-5 py-2 text-gray-500 italic">
                          No results found
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
              <input
                type={isNumerical ? "number" : "text"}
                value={row.value}
                onChange={(e) => handleValueChange(row.id, e.target.value)}
                className="w-full md:w-1/2 py-2 px-4 rounded-lg border border-gray-300 text-base transition focus:ring-2 focus:ring-blue-400"
                placeholder={placeholder || "Enter constant value"}
              />
              {index > 0 && (
                <button
                  onClick={() => handleRemoveRow(row.id)}
                  className="p-2 flex items-center justify-center rounded-full hover:bg-red-100 transition"
                  aria-label="Remove feature"
                  title="Remove feature"
                >
                  <svg viewBox="0 0 24 24" className="w-5 h-5">
                    <path
                      d="M18 6L6 18M6 6l12 12"
                      stroke="red"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                  </svg>
                </button>
              )}
            </div>
          );
        })}
      </div>
      <button
        onClick={handleAddFeature}
        disabled={!hasAvailableFeatures || hasEmptyRows}
        className={`mt-6 bg-blue-500 text-white py-3 px-6 rounded-lg text-base font-semibold w-full max-w-xs mx-auto block transition-colors hover:bg-blue-600 ${
          !hasAvailableFeatures || hasEmptyRows
            ? "opacity-50 cursor-not-allowed"
            : ""
        }`}
      >
        Add Feature
      </button>
    </div>
  );
}
