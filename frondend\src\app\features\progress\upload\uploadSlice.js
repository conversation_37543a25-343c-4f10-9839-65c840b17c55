import { createSlice } from "@reduxjs/toolkit";
import { weatherData } from "../../../../utils/weather";
// import { highSalary } from "../../../../utils/highSalary";

function buildInitialFeatures(dataset) {
  const keys = dataset && dataset.length > 0 ? Object.keys(dataset[0]) : [];
  return {
    allFeatureOrder: keys.map((key, idx) => `${key}-${idx}`), // <-- ['MinTemp-0', 'MaxTemp-1']
    unassigned: {
      id: "unassigned",
      title: "Unassigned Features",
      /*
        items:[
          {
            id: "RainTomorrow-21", 
            feature: "RainTomorrow", 
            uniqueValue: ['Yes', 'No'], 
            disabled: false
          }
        ],
      */
      items: keys.map((key, idx) => ({
        id: `${key}-${idx}`,
        feature: key,
        // Set value to unique values of this feature in the dataset
        uniqueValue: Array.from(new Set(dataset.map((row) => row[key]))),
        disabled: false, // Initially not disabled
      })),
    },
    numerical: {
      id: "numerical",
      title: "Numerical Feature",
      items: [],
    },
    nominal: {
      id: "nominal",
      title: "Nominal Feature",
      items: [],
    },
    ordinal: {
      id: "ordinal",
      title: "Ordinal Feature",
      items: [],
    },
  };
}

// Utility to convert string numbers to numbers
function tryConvertToNumber(value) {
  if (typeof value === "string" && value.trim() !== "" && !isNaN(value)) {
    return Number(value);
  }
  return value;
}

// Utility to convert string numbers to numbers
function normalizeDataset(dataset) {
  return dataset.map((row) => {
    const newRow = {};
    Object.entries(row).forEach(([key, value]) => {
      newRow[key] = tryConvertToNumber(value);
    });
    return newRow;
  });
}

const initialState = {
  file: null,
  showTable: false,
  dataset: normalizeDataset(weatherData),
  selectedColumn: null,
  columnName: "",
  toggledItems: {},
  features: buildInitialFeatures(normalizeDataset(weatherData)), // <-- add this line
};

// console.log(
//   "initialState.features.unassigned.items :",
//   initialState.features.unassigned.items
// );

const uploadSlice = createSlice({
  name: "upload",
  initialState,
  reducers: {
    handleFileUpload: (state, action) => {
      const uploadedFile = action.payload;
      if (uploadedFile) {
        // ...parse file to JS objects as parsedData...
        state.dataset = normalizeDataset(parsedData); // NEW: normalize values
        state.file = uploadedFile;
        alert("File uploaded successfully!");
      }
    },
    toggleTableView: (state) => {
      if (state.file) {
        state.showTable = !state.showTable;
      } else {
        alert("Please upload a file first.");
      }
    },
    toggleItem: (state, action) => {
      const itemId = action.payload;
      let foundItem = null;
      let foundGroup = null;

      // Find the item and its group
      ["unassigned", "numerical", "nominal", "ordinal"].forEach((group) => {
        const idx = state.features[group].items.findIndex(
          (item) => item.id === itemId
        );
        if (idx !== -1) {
          foundItem = state.features[group].items[idx];
          foundGroup = group;
        }
      });

      if (!foundItem) return;

      // Toggle disabled
      const newDisabled = !foundItem.disabled;

      // Remove from all groups
      ["unassigned", "numerical", "nominal", "ordinal"].forEach((group) => {
        state.features[group].items = state.features[group].items.filter(
          (item) => item.id !== itemId
        );
      });

      if (newDisabled) {
        // Add back to its original group, but as disabled
        state.features[foundGroup].items.push({ ...foundItem, disabled: true });
      } else {
        // Insert into unassigned at the correct original order index
        const order = state.features.allFeatureOrder;
        const insertIdx = order.findIndex((id) => id === itemId);
        if (insertIdx === -1) {
          // fallback: append if not found
          state.features.unassigned.items.push({
            ...foundItem,
            disabled: false,
          });
        } else {
          // Insert at the correct position
          state.features.unassigned.items.splice(insertIdx, 0, {
            ...foundItem,
            disabled: false,
          });
          // Remove duplicates if any (keep first occurrence)
          const seen = new Set();
          state.features.unassigned.items =
            state.features.unassigned.items.filter((item) => {
              if (seen.has(item.id)) return false;
              seen.add(item.id);
              return true;
            });
        }
      }
    },
    handleColumnSelect: (state, action) => {
      const index = action.payload;
      const keys = Object.keys(state.dataset[0] || {});
      if (state.selectedColumn === index) {
        state.selectedColumn = null;
        state.columnName = "";
      } else {
        state.selectedColumn = index;
        state.columnName = keys[index];
      }
    },
    handleColumnNameChange: (state, action) => {
      state.columnName = action.payload;
    },
    handleColumnUpdate: (state) => {
      if (state.selectedColumn !== null) {
        const keys = Object.keys(state.dataset[0] || {});
        const oldKey = keys[state.selectedColumn];
        const newKey = state.columnName;

        if (oldKey !== newKey) {
          const updatedDataset = state.dataset.map((row) => {
            const updatedRow = { ...row, [newKey]: row[oldKey] };
            delete updatedRow[oldKey];
            return updatedRow;
          });

          // Preserve the column order
          const reorderedDataset = updatedDataset.map((row) => {
            const reorderedRow = {};
            keys.forEach((key, index) => {
              reorderedRow[index === state.selectedColumn ? newKey : key] =
                row[index === state.selectedColumn ? newKey : key];
            });
            return reorderedRow;
          });

          state.dataset = reorderedDataset;
        }

        state.selectedColumn = null;
      }
    },
    moveFeature: (state, action) => {
      const { source, destination } = action.payload;
      if (source.droppableId === destination.droppableId) {
        // Reorder within the same group
        const items = [...state.features[source.droppableId].items];
        const [removed] = items.splice(source.index, 1);
        items.splice(destination.index, 0, removed);
        state.features[source.droppableId].items = items;
      } else {
        // Move between groups
        const sourceItems = [...state.features[source.droppableId].items];
        const destItems = [...state.features[destination.droppableId].items];
        const [removed] = sourceItems.splice(source.index, 1);
        destItems.splice(destination.index, 0, removed);
        state.features[source.droppableId].items = sourceItems;
        state.features[destination.droppableId].items = destItems;
      }
    },
    updateOrdinalOrder: (state, action) => {
      const { feature, newOrder } = action.payload;
      const item = state.features.ordinal.items.find(
        (i) => i.feature === feature
      );
      if (item) {
        item.uniqueValue = newOrder;
      }
    },
    resetFeatures: (state) => {
      // Move all features from numerical, nominal, and ordinal to unassigned
      const movedItems = [
        ...state.features.numerical.items,
        ...state.features.nominal.items,
        ...state.features.ordinal.items,
      ];
      // Keep existing unassigned items and append moved items
      state.features.unassigned.items = [
        ...state.features.unassigned.items,
        ...movedItems,
      ];
      // Clear other groups
      state.features.numerical.items = [];
      state.features.nominal.items = [];
      state.features.ordinal.items = [];
    },
  },
});

export const {
  handleFileUpload,
  toggleTableView,
  toggleItem,
  handleColumnSelect,
  handleColumnNameChange,
  handleColumnUpdate,
  moveFeature,
  updateOrdinalOrder,
  resetFeatures, // <-- add this
} = uploadSlice.actions;

export default uploadSlice.reducer;
