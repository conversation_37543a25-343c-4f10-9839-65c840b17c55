/**
 * Authentication API service
 * 
 * Handles all authentication-related API calls including:
 * - User login/logout
 * - User registration
 * - Token management
 * - Password reset
 */

import apiService from './api';

const authApi = {
  /**
   * User login
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.email - User email
   * @param {string} credentials.password - User password
   * @returns {Promise} API response with user data and token
   */
  login: (credentials) => {
    return apiService.post('/auth/login', credentials);
  },

  /**
   * User registration
   * @param {Object} userData - Registration data
   * @param {string} userData.email - User email
   * @param {string} userData.password - User password
   * @param {string} userData.name - User full name
   * @returns {Promise} API response with user data
   */
  register: (userData) => {
    return apiService.post('/auth/register', userData);
  },

  /**
   * User logout
   * @returns {Promise} API response
   */
  logout: () => {
    return apiService.post('/auth/logout');
  },

  /**
   * Refresh authentication token
   * @returns {Promise} API response with new token
   */
  refreshToken: () => {
    return apiService.post('/auth/refresh');
  },

  /**
   * Get current user profile
   * @returns {Promise} API response with user profile data
   */
  getProfile: () => {
    return apiService.get('/auth/profile');
  },

  /**
   * Update user profile
   * @param {Object} profileData - Updated profile data
   * @returns {Promise} API response
   */
  updateProfile: (profileData) => {
    return apiService.put('/auth/profile', profileData);
  },

  /**
   * Change user password
   * @param {Object} passwordData - Password change data
   * @param {string} passwordData.currentPassword - Current password
   * @param {string} passwordData.newPassword - New password
   * @returns {Promise} API response
   */
  changePassword: (passwordData) => {
    return apiService.put('/auth/change-password', passwordData);
  },

  /**
   * Request password reset
   * @param {string} email - User email
   * @returns {Promise} API response
   */
  requestPasswordReset: (email) => {
    return apiService.post('/auth/forgot-password', { email });
  },

  /**
   * Reset password with token
   * @param {Object} resetData - Password reset data
   * @param {string} resetData.token - Reset token
   * @param {string} resetData.newPassword - New password
   * @returns {Promise} API response
   */
  resetPassword: (resetData) => {
    return apiService.post('/auth/reset-password', resetData);
  },

  /**
   * Verify email address
   * @param {string} token - Email verification token
   * @returns {Promise} API response
   */
  verifyEmail: (token) => {
    return apiService.post('/auth/verify-email', { token });
  },

  /**
   * Check if user is authenticated
   * @returns {Promise} API response with authentication status
   */
  checkAuth: () => {
    return apiService.get('/auth/check');
  },
};

export default authApi;
