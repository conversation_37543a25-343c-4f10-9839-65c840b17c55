/**
 * Entry point for the React application.
 * This file renders the root component of the application into the DOM.
 */

import React from "react";
import { createRoot } from "react-dom/client";
import App from "./App.jsx";
import "./index.css";
import { store } from "./app/store.js";
import { Provider } from "react-redux";
import * as Tooltip from '@radix-ui/react-tooltip';

/**
 * Renders the React application into the DOM.
 *
 * @function
 * @param {HTMLElement} root - The root DOM element where the React app is mounted.
 * @returns {void}
 */
createRoot(document.getElementById("root")).render(
  /**
   * Wraps the application with the Redux Provider to pass the store to all components.
   */
  <Provider store={store}>
    <Tooltip.Provider>
    <App />
    </Tooltip.Provider>
  </Provider>
);
