from flask import Blueprint, request, jsonify
import pandas as pd
from db import init_db
from bson import ObjectId
from sklearn.impute import SimpleImputer

missingValue_bp = Blueprint('missingValue', __name__)

# MongoDB
from flask import Flask
app = Flask(__name__)
mongo = init_db(app)

# ---------------- Function Drop Rows ----------------
def drop_rows(df, drop_row_features, drop_rows_threshold_value):
    # Calculate the percentage of nulls in the selected features for each row
    percent_null = df[drop_row_features].isnull().mean(axis=1) * 100
    # Keep rows where the percent of nulls is less than the threshold
    return df[percent_null < drop_rows_threshold_value]

# ---------------- Function Drop Columns ----------------
def drop_columns(df, drop_column_features, drop_columns_threshold_value):
    for feature in drop_column_features:
        if feature in df.columns:
            # ลบคอลัมน์ที่มีค่าหายไปเกินกว่า threshold
            if df[feature].isnull().sum() > drop_columns_threshold_value:
                df = df.drop(columns=[feature])
    return df

# ---------------- Function Impute Missing Values ----------------
def impute_missing_values(df, numerical_missing_value_methods, categorical_missing_value_methods):
    # การเติมค่าหายไปในคอลัมน์ที่เป็น numerical
    for method in numerical_missing_value_methods:
        if method['method'] == "Mean Imputation":
            for feature in method['features']:
                if isinstance(feature, dict):
                    feature_name = feature['feature']
                else:
                    feature_name = feature
                if feature_name in df.columns:
                    imputer = SimpleImputer(strategy="mean")
                    df[feature_name] = imputer.fit_transform(df[[feature_name]])

        elif method['method'] == "Median Imputation":
            for feature in method['features']:
                if isinstance(feature, dict):
                    feature_name = feature['feature']
                else:
                    feature_name = feature
                if feature_name in df.columns:
                    imputer = SimpleImputer(strategy="median")
                    df[feature_name] = imputer.fit_transform(df[[feature_name]])

        elif method['method'] == "Constant Imputation":
            for feature in method['features']:
                if isinstance(feature, dict):
                    feature_name = feature['feature']
                    value = feature.get('value', 0)  # Default to 0 if no value is provided
                else:
                    feature_name = feature
                    value = 0  # Default to 0 if no value is provided
                if feature_name in df.columns:
                    imputer = SimpleImputer(strategy="constant", fill_value=value)
                    df[feature_name] = imputer.fit_transform(df[[feature_name]])

    # การเติมค่าหายไปในคอลัมน์ที่เป็น categorical
    for method in categorical_missing_value_methods:
        if method['method'] == "Most Frequent Imputation":
            for feature in method['features']:
                if isinstance(feature, dict):
                    feature_name = feature['feature']
                else:
                    feature_name = feature
                if feature_name in df.columns:
                    imputer = SimpleImputer(strategy="most_frequent")
                    df[feature_name] = imputer.fit_transform(df[[feature_name]]).ravel()
                    
        elif method['method'] == "Constant Imputation":
            for feature in method['features']:
                if isinstance(feature, dict):
                    feature_name = feature['feature']
                    value = feature.get('value', 'Missing')  # Default to 'Missing' if no value is provided
                else:
                    feature_name = feature
                    value = 'Missing'  # Default to 'Missing' if no value is provided
                if feature_name in df.columns:
                    imputer = SimpleImputer(strategy="constant", fill_value=value)
                    df[feature_name] = imputer.fit_transform(df[[feature_name]]).ravel()
    return df   

# ---------------- ROUTE Missing Values ----------------
@missingValue_bp.route('/missingvalue', methods=['POST'])
def handle_missing_value():
    try:
        # รับข้อมูลจากผู้ใช้
        data = request.get_json()
        dataset_id = data.get("dataset_id")
        selected_missing_value_option = data.get("selectedMissingValueOption")
        drop_row_features = data.get("dropRowFeatures", [])  # คอลัมน์ที่จะลบแถวที่มีค่าขาด
        drop_column_features = data.get("dropColumnFeatures", [])  # คอลัมน์ที่จะลบทั้งหมด
        numerical_missing_value_methods = data.get("numericalMissingValueMethods", [])  # วิธีการเติมค่าที่หายไปสำหรับข้อมูลตัวเลข
        categorical_missing_value_methods = data.get("categoricalMissingValueMethods", [])  # วิธีการเติมค่าที่หายไปสำหรับข้อมูลประเภทหมวดหมู่
        drop_rows_threshold_value = data.get("dropRowsThresholdValue", 50)  # ค่าธรณีที่ใช้ในการลบแถว
        drop_columns_threshold_value = data.get("dropColumnsThresholdValue", 50)  # ค่าธรณีที่ใช้ในการลบคอลัมน์

        # Load current ถ้าไม่มีใช้ previous
        current_doc = mongo.db.current.find_one({"dataset_id": ObjectId(dataset_id)})
        if current_doc:
            df = pd.DataFrame(current_doc["data"])
            previous_data = current_doc["data"]
        else:
            previous_doc = mongo.db.previous.find_one({"dataset_id": ObjectId(dataset_id)})
            if not previous_doc:
                return jsonify({"msg": "Dataset not found"}), 404
            df = pd.DataFrame(previous_doc["data"])
            previous_data = previous_doc["data"]

        # Handle feature
        if selected_missing_value_option == "drop rows":
            df = drop_rows(df, drop_row_features, drop_rows_threshold_value)
        elif selected_missing_value_option == "drop columns":
            df = drop_columns(df, drop_column_features, drop_columns_threshold_value)
        elif selected_missing_value_option == "impute missing values":
            df = impute_missing_values(df, numerical_missing_value_methods, categorical_missing_value_methods)

        result_data = df.to_dict(orient="records")

        # Save previous data
        mongo.db.previous.update_one(
            {"dataset_id": ObjectId(dataset_id)},
            {"$set": {"data": previous_data}},
            upsert=True
        )

        # Update current with cleaned data
        result = mongo.db.current.update_one(
            {"dataset_id": ObjectId(dataset_id)},
            {"$set": {"data": result_data}},
            upsert=True
        )

        return jsonify({
            "message": "Missing values handled successfully",
            "data": result_data
        }), 200

    except Exception as e:
        print(f"Error occurred: {str(e)}")
        return jsonify({"error": str(e)}), 500