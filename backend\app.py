from flask import Flask
from dotenv import load_dotenv
from flask_jwt_extended import JWTManager
from db import init_db  # นำเข้า init_db จาก db.py
from flask_cors import CORS
import os

load_dotenv()

app = Flask(__name__)
CORS(app)

# JWT
app.config["JWT_SECRET_KEY"] = os.getenv("SECRET_KEY")
jwt = JWTManager(app)

# MongoDB
mongo = init_db(app)  # ใช้ init_db เชื่อมต่อ MongoDB

# นำเข้า Blueprint จาก auth_routes.py
from routes.auth_routes import auth_bp
from routes.dataset import dataset_bp
from routes.cleansing.missingValue import missingValue_bp
from routes.cleansing.scaling import scaling_bp
from routes.cleansing.encoding import encoding_bp
from routes.cleansing.duplicates import duplicate_bp
from routes.cleansing.dimensionalityReduction import dimensionalityReduction_bp
from routes.cleansing.missingValue import missingValue_bp
from routes.cleansing.outlierValue import outlierValue_bp
from routes.cleansing.typo1 import typo1_bp
from routes.cleansing.typo2 import typo2_bp
from routes.train import train_bp
from routes.predict import predict_bp
from routes.unsupervisedLearning import unsupervised_bp

app.register_blueprint(auth_bp, url_prefix='/auth')
app.register_blueprint(dataset_bp, url_prefix='/dataset')
app.register_blueprint(scaling_bp, url_prefix='/cleansing')
app.register_blueprint(encoding_bp, url_prefix='/cleansing')
app.register_blueprint(duplicate_bp, url_prefix='/cleansing')
app.register_blueprint(dimensionalityReduction_bp, url_prefix='/cleansing')
app.register_blueprint(missingValue_bp, url_prefix='/cleansing')
app.register_blueprint(outlierValue_bp, url_prefix='/cleansing')
app.register_blueprint(typo1_bp, url_prefix='/cleansing')
app.register_blueprint(typo2_bp, url_prefix='/cleansing')
app.register_blueprint(train_bp, url_prefix='/train')
app.register_blueprint(predict_bp, url_prefix='/predicts')
app.register_blueprint(unsupervised_bp, url_prefix='/unsupervised')

@app.route('/')
def home():
    return {'message': 'Flask backend is connected to MongoDB'}

if __name__ == '__main__':
    app.run(debug=True)