import React from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  handleFileUpload,
  toggleTableView,
  handleColumnSelect,
  handleColumnNameChange,
  handleColumnUpdate,
} from "../../../../app/features/progress/upload/uploadSlice";
// นำเข้า action ต่าง ๆ จาก Redux slice ที่ใช้จัดการ state ที่เกี่ยวกับการอัปโหลดและจัดการ dataset

/**
 * DatasetTable is a React component that allows users to upload a dataset,
 * display its contents in a table, and edit column names interactively.
 *
 * It uses Redux state to manage the uploaded file, dataset, view toggling, and column editing.
 *
 * Features:
 * - File upload (supports `.csv` and `.xlsx`, although UI says `.csv` only)
 * - Toggle view between showing/hiding the dataset table
 * - Click on column headers to rename a column
 * - Controlled input for editing selected column name
 * - Scrollable table display with styling
 *
 * Redux State Shape Used:
 * - `state.upload.file`: Uploaded file object
 * - `state.upload.showTable`: Whether the table view is visible
 * - `state.upload.dataset`: Array of dataset rows (objects)
 * - `state.upload.selectedColumn`: Index of the column selected for renaming
 * - `state.upload.columnName`: Temporary name to edit a column name
 *
 * @component
 * @example
 * // Used in a page to manage dataset uploads and quick edits
 * return <DatasetTable />;
 *
 * @returns {JSX.Element} A UI for uploading, viewing, and editing a dataset
 */

const DatasetTable = () => {
  const dispatch = useDispatch(); // ใช้สำหรับเรียก action เพื่อเปลี่ยน state
  const { file, showTable, dataset, selectedColumn, columnName } = useSelector(
    (state) => state.upload
  );

  return (
    <div className="p-6 space-y-6 text-center">
      <h1 className="text-3xl font-bold text-gray-800">Uploaded Dataset</h1>

      <label className="block w-2/3 md:w-1/2 mx-auto border-2 border-gray-400 border-dashed rounded-lg p-10 bg-gray-100 text-gray-600 cursor-pointer">
        <input
          type="file"
          accept=".csv,.xlsx"
          className="hidden"
          onChange={(e) => dispatch(handleFileUpload(e.target.files[0]))} // เมื่อเลือกไฟล์ จะ dispatch handleFileUpload โดยส่งไฟล์เข้า Redux
        />
        <div className="flex flex-col items-center">
          <p className="mt-2">Click to Upload(only .csv)</p>
        </div>
      </label>

      {file && <p className="text-gray-700">Uploaded File: {file.name}</p>}

      <button
        className="bg-blue-600 text-white py-2 px-6 rounded-lg"
        onClick={() => dispatch(toggleTableView())}
      >
        {showTable ? "Hide Dataset" : "View Dataset"}
      </button>

      {/* แสดง form สำหรับแก้ไข column name เฉพาะเมื่อมี column ที่ถูกเลือก */}
      {selectedColumn !== null && (
        <div className="mt-4 p-4 border rounded-lg bg-gray-100">
          {/* แสดงชื่อ column ปัจจุบัน (ก่อนเปลี่ยน) */}
          <h3 className="text-lg font-bold">
            Edit Column "{Object.keys(dataset[0] || [])[selectedColumn]}"
          </h3>
          {/* input สำหรับพิมพ์ชื่อใหม่ */}
          <input
            type="text"
            value={columnName}
            onChange={(e) => dispatch(handleColumnNameChange(e.target.value))}
            className="border px-2 py-1 rounded w-full mt-2"
          />
          <div className="mt-2 flex space-x-4">
            <button
              className="bg-green-600 text-white py-1 px-4 rounded"
              onClick={() => dispatch(handleColumnUpdate())}
            >
              Save
            </button>
          </div>
        </div>
      )}

      {/* แสดงตารางเฉพาะเมื่อ showTable เป็น true */}
      {showTable && (
        <div className="bg-white shadow-md rounded-lg p-6 mt-6">
          <h1 className="text-2xl font-bold mb-4 text-teal-300">
            Hint: Click on column name for change their name
          </h1>
          <h2 className="text-xl font-bold mb-4">Dataset Table</h2>
          <div className="overflow-scroll max-h-96">
            {" "}
            {/* Add a wrapper with restricted height */}
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-200">
                  {Object.keys(dataset[0] || {}).map((key, index) => (
                    <th
                      key={key} // Use the column name as the unique key
                      className={`border border-gray-300 px-4 py-2 cursor-pointer ${selectedColumn === key ? "bg-blue-300" : ""
                        }`}
                      onClick={() => dispatch(handleColumnSelect(index))}
                    >
                      {key}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {dataset.map((row, rowIndex) => (
                  <tr
                    key={`${row.id}-${rowIndex}`} // Combine row.id and rowIndex for a unique key
                    className="text-center border border-gray-300"
                  >
                    {Object.values(row).map((value, cellIndex) => (
                      <td
                        key={`${row.id}-${rowIndex}-${cellIndex}`} // Combine row.id, rowIndex, and cellIndex for unique keys
                        className="border border-gray-300 px-4 py-2"
                      >
                        {value}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default DatasetTable;
