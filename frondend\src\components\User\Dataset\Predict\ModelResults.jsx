import React from "react";

/**
 * ModelResults component for displaying machine learning prediction metrics.
 *
 * This component presents the evaluation results of a trained model,
 * such as accuracy, precision, recall, and F1-score. If available,
 * it also renders a confusion matrix and provides a download link for the results.
 *
 * @component
 * @param {Object} props - Component props
 * @param {Object} props.results - The results object returned from the backend after prediction
 * @param {number} [props.results.accuracy] - Accuracy of the model (0.0 - 1.0)
 * @param {number} [props.results.precision] - Precision score
 * @param {number} [props.results.recall] - Recall score
 * @param {number} [props.results.f1_score] - F1 score
 * @param {number[][]} [props.results.confusion_matrix] - 2D array representing the confusion matrix
 * @param {string} [props.results.downloadLink] - Optional URL to download the result CSV or report
 * @returns {JSX.Element}
 *
 * @example
 * <ModelResults
 *   results={{
 *     accuracy: 0.92,
 *     precision: 0.89,
 *     recall: 0.91,
 *     f1_score: 0.90,
 *     confusion_matrix: [[50, 2], [3, 45]],
 *     downloadLink: "/downloads/results.csv"
 *   }}
 * />
 */

const ModelResults = ({ results }) => {
  if (!results) {
    return (
      <div className="text-gray-500 text-center p-4">
        Run a prediction to see results.
      </div>
    );
  }

  return (
    <div className="bg-white rounded shadow p-6 space-y-4">
      <h2 className="text-xl font-semibold">Model Results</h2>

      {/* Metrics */}
      <div className="grid grid-cols-2 gap-4">
        <div className="p-4 bg-gray-50 rounded">
          <h3 className="text-sm font-medium">Accuracy</h3>
          <p className="text-lg font-bold">{results.accuracy ?? "N/A"}</p>
        </div>
        <div className="p-4 bg-gray-50 rounded">
          <h3 className="text-sm font-medium">Precision</h3>
          <p className="text-lg font-bold">{results.precision ?? "N/A"}</p>
        </div>
        <div className="p-4 bg-gray-50 rounded">
          <h3 className="text-sm font-medium">Recall</h3>
          <p className="text-lg font-bold">{results.recall ?? "N/A"}</p>
        </div>
        <div className="p-4 bg-gray-50 rounded">
          <h3 className="text-sm font-medium">F1 Score</h3>
          <p className="text-lg font-bold">{results.f1_score ?? "N/A"}</p>
        </div>
      </div>

      {/* Optional Confusion Matrix */}
      {results.confusion_matrix && (
        <div>
          <h3 className="text-sm font-medium mb-2">Confusion Matrix</h3>
          <table className="w-full border border-gray-300">
            <tbody>
              {results.confusion_matrix.map((row, rowIndex) => (
                <tr key={rowIndex} className="text-center">
                  {row.map((cell, cellIndex) => (
                    <td key={cellIndex} className="border border-gray-300 p-2">
                      {cell}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Optional Download Button */}
      {results.downloadLink && (
        <div className="mt-4">
          <a
            href={results.downloadLink}
            download
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Download Results
          </a>
        </div>
      )}
    </div>
  );
};

export default ModelResults;
