from pymongo import MongoClient
from bson import ObjectId
from datetime import datetime

client = MongoClient("mongodb://localhost:27017/")
db = client["senior-project"]

# Collections
old_col = db["datasets"]
meta_col = db["datasets_meta"]
previous_col = db["previous"]
current_col = db["current"]
split_col = db["datasets_split"]
model_col = db["datasets_model"]
predict_col = db["datasets_predictions"]

for doc in old_col.find({}):
    dataset_id = doc["_id"]

    # 1️⃣ META
    meta_doc = {
        "_id": dataset_id,
        "current_dataset_name": doc.get("current_dataset_name"),
        "csv_filename": doc.get("csv_filename"),
        "csv_path": doc.get("csv_path", None),
        "target_column": doc.get("target_column", None)
    }
    meta_col.insert_one(meta_doc)

    # 2️⃣ PREVIOUS (raw)
    if "previous_data" in doc:
        previous_col.insert_one({
            "dataset_id": dataset_id,
            "data": doc["previous_data"],
        })

    # 3️⃣ CURRENT (cleansed/trained)
    if "current_data" in doc:
        current_doc = {
            "dataset_id": dataset_id,
            "data": doc["current_data"],
            "updated_at": datetime.utcnow(),
            "history": []
        }
        if "previous_data" in doc:
            current_doc["history"].append({
                "data": doc["previous_data"],
                "note": "migrated from previous_data"
            })
        current_col.insert_one(current_doc)

    # 4️⃣ SPLIT
    if "train_data" in doc or "test_data" in doc or "validation_data" in doc:
        split_col.insert_one({
            "dataset_id": dataset_id,
            "train_data": doc.get("train_data", []),
            "test_data": doc.get("test_data", []),
            "validation_data": doc.get("validation_data", None),
        })

    # 5️⃣ MODEL
    if "model_path" in doc or "last_train_metrics" in doc:
        model_col.insert_one({
            "dataset_id": dataset_id,
            "model_path": doc.get("model_path", None),
            "metrics": doc.get("last_train_metrics", {}),
        })

    # 6️⃣ PREDICTIONS
    if "predictions" in doc:
        predict_col.insert_one({
            "dataset_id": dataset_id,
            "predictions": doc["predictions"],
        })

print("✅ Migration complete using collections: previous / current / meta / etc.")