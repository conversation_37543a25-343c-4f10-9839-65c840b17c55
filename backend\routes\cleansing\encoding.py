from flask import Blueprint, request, jsonify
from db import init_db
import pandas as pd
from bson import ObjectId
from sklearn.preprocessing import LabelEncoder, OneHotEncoder, OrdinalEncoder

# สร้าง Blueprint
encoding_bp = Blueprint('encoding', __name__)

# MongoDB
from flask import Flask
app = Flask(__name__)
mongo = init_db(app)

# --------- Apply LabelEncoder ---------
def apply_label_encoding(df, feature_cols, custom_labels=None):
    encoded_df = df.copy()
    encoders = {}

    # ตรวจสอบ custom labels
    if custom_labels:
        for col, label_map in custom_labels.items():
            if col in df.columns:
                print(f"Custom labels for {col}: {label_map}")

                # แปลงค่าที่ตรงกับ custom labels
                df[col] = df[col].map(label_map).fillna(df[col])

                # ตรวจสอบค่าที่ไม่ได้แปลง (NaN) และใช้ LabelEncoder แปลงค่าเหล่านั้น
                missing_values = df[col].isna()

                if missing_values.any():
                    print(f"Missing values found in {col}, applying LabelEncoder")
                    # เติมค่า NaN ก่อนที่จะแปลงด้วย LabelEncoder
                    df[col] = df[col].fillna('missing')  # เติมค่า 'missing'
                    encoder = LabelEncoder()
                    df[col] = encoder.fit_transform(df[col].astype(str))  # แปลงค่าที่เหลือเป็นตัวเลข
                    encoders[col] = encoder
                print(f"Encoded values for {col}: {df[col].unique()}")
            else:
                raise ValueError(f"Column {col} not found in the dataset")
    else:
        for col in feature_cols:
            if col in df.columns:
                encoder = LabelEncoder()
                df[col] = encoder.fit_transform(df[col].astype(str))  # แปลงข้อมูลในคอลัมน์ให้เป็นตัวเลข
                encoders[col] = encoder
                print(f"Encoded values for {col}: {df[col].unique()}")

    return df, encoders # คืนค่า DataFrame ที่ถูกแปลงและ encoders

# --------- Apply OrdinalEncoder ---------
def apply_ordinal_encoding(df, feature_cols, custom_labels=None):
    encoded_df = df.copy() # คัดลอก DataFrame
    encoders = {}

    for col in feature_cols:
        if custom_labels and col in custom_labels:
            categories = [custom_labels[col]]  # ต้องเป็น list of list สำหรับแต่ละ column
            encoder = OrdinalEncoder(categories=categories)
        else:
            encoder = OrdinalEncoder()
        
        encoded_df[col] = encoder.fit_transform(df[[col]]) # แปลงคอลัมน์
        encoders[col] = encoder

    return encoded_df, encoders # คืนค่า DataFrame ที่ถูกแปลงและ encoders

# --------- Apply OneHotEncoder ---------
def apply_onehot_encoding(df, feature_cols, drop_first=False, column_options=None):
    encoded_df = df.copy() # คัดลอก DataFrame
    encoders = {}

    for col in feature_cols:
        encoder = OneHotEncoder(sparse_output=False, drop='first' if drop_first else None) # ใช้ drop_first สำหรับ multicollinearity
        transformed = encoder.fit_transform(df[[col]]) # ทำการแปลงข้อมูล

        prefix = ""
        if column_options and col in column_options:
            prefix = column_options[col].get("prefix", f"{col}")
        
        col_names = encoder.get_feature_names_out([col])
        renamed_cols = [col_name.replace(f"{col}_", f"{prefix}_") for col_name in col_names] # ตั้งชื่อคอลัมน์ใหม่

        one_hot_df = pd.DataFrame(transformed, columns=renamed_cols) # สร้าง DataFrame ใหม่สำหรับค่าที่แปลงแล้ว
        encoded_df = pd.concat([encoded_df, one_hot_df], axis=1).drop(columns=[col]) # รวมผลการแปลง

        encoders[col] = encoder

    return encoded_df, encoders # คืนค่า DataFrame ที่ถูกแปลงและ encoders

# --------- Route Encoding ---------
@encoding_bp.route('/encoding', methods=['POST'])
def train_model():

    # รับข้อมูลจากผู้ใช้
    dataset_id = request.json.get("dataset_id")
    encoding = request.json.get("encoding") # เลือกประเภท -> label, ordinal, onehot
    selected_columns = request.json.get("columns") # คอลัมน์ที่ผู้ใช้เลือก
    custom_labels = request.json.get("custom_labels", {}) # กำหนดค่า value ใน column ที่เลือก
    multinearity = request.json.get("multinearity", False) # ใช้ drop_first

    # ตรวจสอบว่าผู้ใช้เลือกคอลัมน์หรือไม่
    if not selected_columns:
        return jsonify({"msg": "No columns selected for encoding"}), 400

    # แปลง columns ที่เลือก
    column_options = {}
    for col_item in selected_columns:
        if isinstance(col_item, dict):
            for col_name, options in col_item.items():
                column_options[col_name] = options
        elif isinstance(col_item, str):
            column_options[col_item] = {}
        else:
            return jsonify({"msg": "Invalid column format"}), 400

    selected_column_names = list(column_options.keys()) # คอลัมน์ที่เลือก

    # Load current ถ้าไม่มีใช้ previous
    current_doc = mongo.db.current.find_one({"dataset_id": ObjectId(dataset_id)})
    if current_doc:
        df = pd.DataFrame(current_doc["data"])
        previous_data = current_doc["data"]
    else:
        previous_doc = mongo.db.previous.find_one({"dataset_id": ObjectId(dataset_id)})
        if not previous_doc:
            return jsonify({"msg": "Dataset not found"}), 404
        df = pd.DataFrame(previous_doc["data"])
        previous_data = previous_doc["data"]

    # ตรวจสอบคอลัมน์ที่เลือกว่ามีอยู่ใน dataset หรือไม่
    missing_cols = [col for col in selected_column_names if col not in df.columns]
    if missing_cols:
        return jsonify({"msg": f"Missing columns: {', '.join(missing_cols)}"}), 400

    # เลือกประเภท encode
    if encoding == "label":
        encoded_df, encoders = apply_label_encoding(df, selected_column_names, custom_labels)
    elif encoding == "ordinal":
        encoded_df, encoders = apply_ordinal_encoding(df, selected_column_names, custom_labels)
    elif encoding == "onehot":
        encoded_df, encoders = apply_onehot_encoding(df, selected_column_names, multinearity, column_options)
    else:
        return jsonify({"msg": "Invalid encoding type"}), 400

    # เก็บข้อมูลปัจจุบันไปที่ previous
    mongo.db.previous.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": previous_data}},
        upsert=True
    )

    # อัปเดต current ด้วย encoded data
    mongo.db.current.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": encoded_df.to_dict(orient='records')}},
        upsert=True
    )

    return jsonify({
        "encode_data": encoded_df.to_dict(orient='records')
    }), 200
