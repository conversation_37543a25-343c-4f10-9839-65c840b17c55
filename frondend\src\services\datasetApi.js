/**
 * Dataset API service
 * 
 * Handles all dataset-related API calls including:
 * - Dataset upload
 * - Dataset retrieval
 * - Dataset management
 */

import apiService from './api';

const datasetApi = {
  /**
   * Upload a new dataset
   * @param {FormData} formData - Form data containing the dataset file and metadata
   * @param {Function} onUploadProgress - Progress callback function
   * @returns {Promise} API response
   */
  uploadDataset: (formData, onUploadProgress = null) => {
    return apiService.uploadFile('/dataset/upload', formData, onUploadProgress);
  },

  /**
   * Get all datasets for the current user
   * @returns {Promise} API response with datasets list
   */
  getDatasets: () => {
    return apiService.get('/dataset/list');
  },

  /**
   * Get a specific dataset by ID
   * @param {string} datasetId - Dataset ID
   * @returns {Promise} API response with dataset data
   */
  getDataset: (datasetId) => {
    return apiService.get(`/dataset/${datasetId}`);
  },

  /**
   * Delete a dataset
   * @param {string} datasetId - Dataset ID to delete
   * @returns {Promise} API response
   */
  deleteDataset: (datasetId) => {
    return apiService.delete(`/dataset/${datasetId}`);
  },

  /**
   * Update dataset metadata
   * @param {string} datasetId - Dataset ID
   * @param {Object} metadata - Updated metadata
   * @returns {Promise} API response
   */
  updateDataset: (datasetId, metadata) => {
    return apiService.put(`/dataset/${datasetId}`, metadata);
  },

  /**
   * Get dataset statistics
   * @param {string} datasetId - Dataset ID
   * @returns {Promise} API response with statistics
   */
  getDatasetStats: (datasetId) => {
    return apiService.get(`/dataset/${datasetId}/stats`);
  },

  /**
   * Preview dataset (first few rows)
   * @param {string} datasetId - Dataset ID
   * @param {number} limit - Number of rows to preview (default: 10)
   * @returns {Promise} API response with preview data
   */
  previewDataset: (datasetId, limit = 10) => {
    return apiService.get(`/dataset/${datasetId}/preview`, { limit });
  },

  /**
   * Download dataset as CSV
   * @param {string} datasetId - Dataset ID
   * @returns {Promise} API response with file blob
   */
  downloadDataset: (datasetId) => {
    return apiService.downloadFile(`/dataset/${datasetId}/download`);
  },
};

export default datasetApi;
