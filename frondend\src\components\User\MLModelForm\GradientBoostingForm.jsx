import React, { useState, useEffect } from "react";

const GradientBoostingForm = ({ onParamsChange }) => {
  const [params, setParams] = useState({
    learning_rate: 0.05,
    n_estimators: 200,
    subsapmle: 0.7,
  });

  useEffect(() => {
    onParamsChange(params);
  }, [params, onParamsChange]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setParams((prevParams) => ({
      ...prevParams,
      [name]: value,
    }));
  };

  return (
    <div className="space-y-2 p-4 bg-white rounded shadow">
      <div>
        <label className="block text-sm font-medium">learning_rate:</label>
        <input
          type="number"
          name="learning_rate"
          value={params.learning_rate}
          onChange={handleChange}
          className="mt-1 p-2 border rounded w-full"
          step="0.1"
        />
      </div>

      <div>
        <label className="block text-sm font-medium">n_estimators:</label>
        <input
          type="number"
          name="n_estimators"
          value={params.n_estimators}
          onChange={handleChange}
          className="mt-1 p-2 border rounded w-full"
          step="100"
        />
      </div>

      <div>
        <label className="block text-sm font-medium">subsample:</label>
        <input
          type="number"
          name="subsapmle"
          value={params.subsapmle}
          onChange={handleChange}
          className="mt-1 p-2 border rounded w-full"
          step="0.1"
        />
      </div>
    </div>
  );
};

export default GradientBoostingForm;
