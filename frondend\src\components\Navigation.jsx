/**
 * Simple Navigation Component for Testing Routes
 */

import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const Navigation = () => {
  const location = useLocation();

  const routes = [
    { path: '/', label: 'Home' },
    { path: '/datasets', label: 'Datasets' },
    { path: '/cleansing', label: 'Cleansing' },
    { path: '/visualization', label: 'Visualization' },
    { path: '/statistics', label: 'Statistics' },
    { path: '/ml', label: 'ML' },
    { path: '/api-test', label: 'API Test' },
    { path: '/examples/upload', label: 'Upload Example' },
    { path: '/examples/list', label: 'List Example' },
  ];

  return (
    <nav className="bg-blue-600 text-white p-4">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-xl font-bold mb-4">ML Data Platform</h1>
        <div className="flex flex-wrap gap-2">
          {routes.map((route) => (
            <Link
              key={route.path}
              to={route.path}
              className={`px-3 py-1 rounded text-sm ${
                location.pathname === route.path
                  ? 'bg-blue-800 font-semibold'
                  : 'bg-blue-500 hover:bg-blue-700'
              }`}
            >
              {route.label}
            </Link>
          ))}
        </div>
        <div className="mt-2 text-sm opacity-75">
          Current path: {location.pathname}
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
