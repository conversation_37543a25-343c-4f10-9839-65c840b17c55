import React, { useState, useEffect } from "react";

const LassoRegressionForm = ({ onParamsChange }) => {
    const [params, setParams] = useState({
        alpha: 0.01,
        max_iter: 1000,
        tol: "1e_4",
    });

    useEffect(() => {
        onParamsChange(params);
    }, [params, onParamsChange]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setParams((prevParams) => ({
            ...prevParams,
            [name]: value,
        }));
    };

    return (
        <div className="space-y-2 p-4 bg-white rounded shadow">

            <div>
                <label className="block text-sm font-medium">alpha:</label>
                <input
                    type="number"
                    name="alpha"
                    value={params.alpha}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="0.01"
                />
            </div>

            <div>
                <label className="block text-sm font-medium">max_iter:</label>
                <input
                    type="number"
                    name="max_iter"
                    value={params.max_iter}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="1000"
                />
            </div>

            <div>
                <label className="block text-sm font-medium">tol:</label>
                <select
                    name="tol"
                    value={params.tol}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="1e_4">1e-4</option>
                    <option value="1e_3o">1e-3</option>
                </select>
            </div>
        </div>
    );
};

export default LassoRegressionForm;
