from flask import Blueprint, json, request, jsonify
from db import init_db
import os
import pandas as pd
from bson import ObjectId
from datetime import datetime
import pytz
import io
import csv

# สร้าง Blueprint สำหรับ Dataset
dataset_bp = Blueprint('dataset', __name__)

# MongoDB
from flask import Flask
app = Flask(__name__)
mongo = init_db(app)

# เซตเวลาประเทศไทย
bangkok_tz = pytz.timezone('Asia/Bangkok') 
def get_current_time():
    return datetime.now(bangkok_tz).strftime('%Y-%m-%dT%H:%M:%SZ')

@dataset_bp.route('/upload', methods=['POST'])
def upload_dataset():
    json_data = request.form.get('json_data') 

    try:
        data = json.loads(json_data)
        
        if 'current_dataset_name' not in data:
            return jsonify({"error": "current_dataset_name is missing in json_data"}), 400
        
        # เลือกไฟล์ csv 
        current_data = request.files.get('current_data')
        if not current_data or current_data.filename == '':
            return jsonify({"error": "No selected CSV file"}), 400
        if not current_data.filename.endswith('.csv'):
            return jsonify({"error": "Only CSV files are allowed"}), 400

        # แปลงไฟล์ CSV เป็น list of dict
        df = pd.read_csv(current_data, encoding='utf-8-sig')
        data_from_csv = df.to_dict(orient='records')

        if data_from_csv:

            timestamp = datetime.now(bangkok_tz).strftime('%Y-%m-%d_%H-%M-%S')
            original_filename = current_data.filename
            filename, file_extension = os.path.splitext(original_filename)
            saved_filename = f"{timestamp}_{filename}{file_extension}"

            # เก็บ metadata ลง datasets_meta
            dataset_meta = {
                "current_dataset_name": data.get("current_dataset_name"),
                "csv_filename": saved_filename,
            }
            # ตัวเชื่อม ID ไปใช้กับ collection อื่น
            dbResponse = mongo.db.datasets_meta.insert_one(dataset_meta)
            dataset_id = dbResponse.inserted_id

            # เก็บ raw data ลง previous
            mongo.db.previous.insert_one({
                "dataset_id": dataset_id,
                "data": data_from_csv,
            })

            # ส่งค่า response กลับมา
            return jsonify({
                "msg": "Dataset uploaded successfully",
                "dataset_id": str(dataset_id),
            }), 201

    except Exception as e:
        return jsonify({"msg": "Error processing file", "error": str(e)}), 500

# ------------------------------------------------------ ยังไม่ได้ใช้ ------------------------------------------------------

# --------- Route Get Dataset List ---------
# @dataset_bp.route('/list_train_datasets', methods=['GET'])
# @jwt_required()
# def list_train_datasets():
#     current_user = get_jwt_identity()
#     user = mongo.db.users.find_one({"username": current_user})

#     if user:
#         # ดึงข้อมูล dataset ที่ผ่านการ train แล้วจาก MongoDB
#         trained_datasets = mongo.db.trained_datasets.find()
#         dataset_list = []
#         for dataset in trained_datasets:
#             dataset_list.append({
#                 "dataset_name": dataset.get("dataset_name"),
#                 "model_type": dataset.get("model_type"),
#                 "created_at": dataset.get("created_at"),
#                 "performance": dataset.get("performance"),
#                 "parameters": dataset.get("parameters")
#             })

#         return jsonify(dataset_list), 200

#     return jsonify({"msg": "Unauthorized access"}), 403

# --------- Route Get Dataset by ID ---------
# @dataset_bp.route('/get_datasets/<dataset_id>', methods=['GET'])
# @jwt_required()
# def get_dataset(dataset_id):
#     try:
#         dataset = mongo.db.datasets.find_one({"_id": ObjectId(dataset_id)})
#         if not dataset:
#             return jsonify({"msg": "Dataset not found"}), 404

#         return jsonify({
#             "cleansing data": dataset.get("current_data", [])
#         }), 200
#     except Exception as e:
#         return jsonify({"msg": "Server error", "error": str(e)}), 500

# --------- Route List Cleansing Data ---------
# @dataset_bp.route('/list_cleansing', methods=['GET'])
# @jwt_required()
# def list_cleansing():
#     try:
#         current_user = get_jwt_identity()
#         user = mongo.db.users.find_one({"username": current_user})
#         if not user:
#             return jsonify({"msg": "Unauthorized"}), 403

#         user_id = user["_id"]
#         datasets = mongo.db.datasets.find({"user_id": user_id})
#         cleansing_list = []

#         for d in datasets:
#             cleansing_list.append({
#                 "cleansing_id": d.get("cleansing_id"),
#                 "user_id": d.get("user_id"),
#                 "cleansing_name": d.get("current_dataset_name"),
#                 "cleansing_at": d.get("uploaded_at")
#             })

#         return jsonify(cleansing_list), 200
#     except Exception as e:
#         return jsonify({"msg": "Server error", "error": str(e)}), 500
    
# --------- Route Download Dataset ---------
# @dataset_bp.route('/datasets/<dataset_id>/download', methods=['GET'])
# @jwt_required()
# def download_dataset(dataset_id):
#     try:
#         dataset = mongo.db.datasets.find_one({"_id": ObjectId(dataset_id)})
#         if not dataset:
#             return jsonify({"msg": "Dataset not found"}), 404

#         rows = dataset.get("current_data", [])
#         if not rows:
#             return jsonify({"msg": "No data to export"}), 400

#         output = io.StringIO()
#         writer = csv.DictWriter(output, fieldnames=rows[0].keys())
#         writer.writeheader()
#         writer.writerows(rows)
#         output.seek(0)

#         return send_file(
#             io.BytesIO(output.getvalue().encode("utf-8-sig")),
#             mimetype='text/csv',
#             as_attachment=True,
#             download_name=f"{dataset['current_dataset_name']}.csv"
#         )

#     except Exception as e:
#         return jsonify({"msg": "Server error", "error": str(e)}), 500