import React, { useState, useEffect } from "react";

const SVCForm = ({ onParamsChange }) => {
    const [params, setParams] = useState({
        C: 1.0,
        kernel: "sigmoid",
        gamma: 1.0,
    });

    useEffect(() => {
        onParamsChange(params);
    }, [params, onParamsChange]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setParams((prevParams) => ({
            ...prevParams,
            [name]: value,
        }));
    };

    return (
        <div className="space-y-2 p-4 bg-white rounded shadow">
            <div>
                <label className="block text-sm font-medium">C (Regularization):</label>
                <input
                    type="number"
                    name="C"
                    value={params.C}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="0.1"
                />
            </div>

            <div>
                <label className="block text-sm font-medium">Kernel:</label>
                <select
                    name="kernel"
                    value={params.kernel}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="linear">Linear</option>
                    <option value="poly">Polynomial</option>
                    <option value="rbf">Radial Basis Function</option>
                    <option value="sigmoid">Sigmoid</option>
                </select>
            </div>

            <div>
                <label className="block text-sm font-medium">Gamma:</label>
                <input
                    type="number"
                    name="gamma"
                    value={params.gamma}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="0.1"
                />
            </div>
        </div>
    );
};

export default SVCForm;
