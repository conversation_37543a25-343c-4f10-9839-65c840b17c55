import React, { useState, useEffect } from "react";

const LinearRegressionForm = ({ onParamsChange }) => {
  const [params, setParams] = useState({
    fit_intercept: "no",
    normalize: "no",
    n_jobs: 0,
  });

  useEffect(() => {
    onParamsChange(params);
  }, [params, onParamsChange]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setParams((prevParams) => ({
      ...prevParams,
      [name]: value,
    }));
  };

  return (
    <div className="space-y-2 p-4 bg-white rounded shadow">
      <div>
        <label className="block text-sm font-medium">fit_intercept:</label>
        <select
          name="fit_intercept"
          value={params.fit_intercept}
          onChange={handleChange}
          className="mt-1 p-2 border rounded w-full"
        >
          <option value="yes">Yes</option>
          <option value="no">No</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium">normalize:</label>
        <select
          name="normalize"
          value={params.normalize}
          onChange={handleChange}
          className="mt-1 p-2 border rounded w-full"
        >
          <option value="yes">Yes</option>
          <option value="no">No</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium">n_jobs:</label>
        <input
          type="number"
          name="n_jobs"
          value={params.n_jobs}
          onChange={handleChange}
          className="mt-1 p-2 border rounded w-full"
          step="1"
        />
      </div>
    </div>
  );
};

export default LinearRegressionForm;
