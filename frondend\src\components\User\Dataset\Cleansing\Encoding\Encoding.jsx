/**
 * Encoding.jsx
 * --------------------------------------------------------------------------
 * React component for configuring encoding of *nominal* and *ordinal* categorical
 * features in a data‑cleaning workflow. It lets users:
 * • Choose an encoding strategy (One‑Hot, Label, Ordinal).
 * • Pick which features to encode.
 * • Edit dummy‑column prefixes (One‑Hot) or numeric codes (Label/Ordinal).
 * • Inspect unique values and their counts for each feature.
 *
 * @module Encoding
 */
import React, { useMemo, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import Dropdown from "../dropdown";
import ApplyTable from "../ApplyTable";

export const cleanUniqueId = (str) => str.replace(/-\d+$/, "");

/**
 * Return the unique string values from an array, preserving insertion order.
 *
 * @template T
 * @param {T[]} [arr=[]] Input array.
 * @returns {string[]} Array of unique values (as strings).
 *
 * How it work:
 * 1. Convert each element in the array to a String.
 * 2. Use the Set object to remove duplicates. return 1,2,3 not [1,2,3].
 * 3. Use spread operator to iterate over the Set object and put each value into an array.
 */
const uniq = (arr = []) => [...new Set(arr.map(String))]; // cast → string แล้วหาค่าไม่ซ้ำ

/** @constant {string[]} */
const nominalEncodingOptions = ["One-Hot encoding"];
/** @constant {string[]} */
const ordinalEncodingOptions = ["Ordinal encoding"];

/* -------------------------------------------------------------------------- */
/* Encoding – main component 	 	 	 	 	 	 	 	 	 	 	 */
/* -------------------------------------------------------------------------- */

/**
 * Main component that orchestrates the encoding configuration workflow.
 *
 * Redux read‑only slices:
 * • `upload` 	→ dataset & feature metadata.
 * • `encoding` → current encoding settings.
 *
 * Redux actions dispatched:
 * • `setEncoding` 	 	 	 	 	– update chosen methods.
 * • `setSelectedNominalFeatures` 	– toggle selected nominal features.
 * • `setSelectedOrdinalFeatures` 	– toggle selected ordinal features.
 * • `setDummyPrefix` 	 	 	 	– store dummy prefixes for One‑Hot columns.
 *
 * @component
 */
const Encoding = () => {
  /* ------------------------- Redux state ------------------------------- */
  const {
    dataset = [], // raw rows
    features: reduxFeatures = {},
  } = useSelector((s) => s.upload);

  /* ------------------------ feature lists ------------------------------ */
  /** @type {Array<{id: string, feature: string, uniqueValue: string[], disabled: boolean}>} */
  const nominalFeatures = useMemo(
    () => (reduxFeatures?.nominal?.items || []).map((f) => ({ ...f })),
    [reduxFeatures?.nominal?.items]
  );

  /** @type {Array<{id: string, feature: string, uniqueValue: string[], disabled: boolean}>} */
  const ordinalFeatures = useMemo(
    () => (reduxFeatures?.ordinal?.items || []).map((f) => ({ ...f })),
    [reduxFeatures?.ordinal?.items]
  );

  /* ------------------------- local state ------------------------------- */
  /** Whether to drop the first dummy column when One‑Hot encoding. */
  const [nominalMethod, setNominalMethod] = useState("");
  const [ordinalMethod, setOrdinalMethod] = useState("");
  const [dropFirstCategory, setDropFirstCategory] = useState(false);
  const [dummyPrefix, setDummyPrefix] = useState({});
  const [selectedOrdinalFeatures, setSelectedOrdinalFeatures] = useState([]);
  const [selectedNominalFeatures, setSelectedNominalFeatures] = useState([]);
  const [nominalMappings, setNominalMappings] = useState({});
  const [ordinalMappings, setOrdinalMappings] = useState({});
  // State for toggle show apply table
  const [showTable, setShowTable] = useState(false);

  /**
   * Build a mapping‑table row set for a given feature, consisting of unique
   * categories, their default numeric codes (0‑based index), and counts.
   *
   * @param {{feature: string, categories?: string[]}} f Feature metadata.
   * @returns {Array<{category: string, code: number, count: number}>}
   */
  const buildRows = (f) => {
    // 1) Use explicitly provided categories if available.
    let cats = Array.isArray(f?.categories) ? f.categories : [];

    // 2) Fallback: extract from raw dataset.
    if (!cats.length && dataset.length) {
      cats = dataset.map((row) => row[f.feature]);
    }

    // 3) Filter null/undefined
    const checkNullUndefCats = cats.filter(
      (v) => v !== null && v !== undefined
    );

    // 4) de‑duplicate.
    const uniques = uniq(checkNullUndefCats);

    // 5) Count occurrences per category.
    const counts = {};
    checkNullUndefCats.forEach((check) => {
      const key = String(check);
      counts[key] = (counts[key] || 0) + 1;
    });

    // 6) Return as array of {category, code, count} objects.
    return uniques.map((cat, idx) => ({
      category: cat,
      code: idx,
      count: counts[cat] ?? 0,
    }));
  };

  /* ------------------- sync mapping tables ----------------------------- */
  useEffect(() => {
    // --- NOMINAL ---
    nominalFeatures.forEach((f) => {
      if (!nominalMappings[f.id]) {
        const mappings = buildRows(f);
        setNominalMappings((prev) => ({ ...prev, [f.id]: mappings }));
      }
    });

    // --- ORDINAL ---
    ordinalFeatures.forEach((f) => {
      if (!ordinalMappings[f.id]) {
        const mappings = buildRows(f);
        setOrdinalMappings((prev) => ({ ...prev, [f.id]: mappings }));
      }
    });
  }, [nominalFeatures, ordinalFeatures, dataset]);

  /**
   * Toggle a nominal feature in the selected list.
   * @param {string} fid Feature ID.
   */
  const toggleNominal = (fid) => {
    const next = selectedNominalFeatures.includes(fid)
      ? selectedNominalFeatures.filter((x) => x !== fid)
      : [...selectedNominalFeatures, fid];
    setSelectedNominalFeatures(next);
  };

  /**
   * Toggle an ordinal feature in the selected list.
   * @param {string} fid Feature ID.
   */
  const toggleOrdinal = (fid) => {
    const next = selectedOrdinalFeatures.includes(fid)
      ? selectedOrdinalFeatures.filter((x) => x !== fid)
      : [...selectedOrdinalFeatures, fid];
    setSelectedOrdinalFeatures(next);
  };

  /**
   * Update the dummy‑column prefix for a specific feature.
   * @param {string} fid Feature ID.
   * @param {string} val New prefix.
   */
  const handlePrefixChange = (fid, val) => {
    const next = { ...dummyPrefix, [fid]: val };
    setDummyPrefix(next);
  };

  useEffect(() => {
    console.log("dummyPrefix :", dummyPrefix);
    console.log("selectedOrdinalFeatures :", selectedOrdinalFeatures);
    console.log("selectedNominalFeatures :", selectedNominalFeatures);
    console.log("nominalMappings :", nominalMappings);
    console.log("ordinalMappings :", ordinalMappings);
  }, [
    dummyPrefix,
    selectedOrdinalFeatures,
    selectedNominalFeatures,
    nominalMappings,
    ordinalMappings,
  ]);

  // =========================== Function for apply duplicate and reset parameter ============================
  // Show table when apply button is clicked
  const handleApply = () => {
    setShowTable(true);
  };

  // Close the table and reset the parameters when reset button is clicked
  const handleResetParameters = () => {
    setNominalMethod("");
    setOrdinalMethod("");
    setDropFirstCategory(false);
    setDummyPrefix({});
    setSelectedOrdinalFeatures([]);
    setSelectedNominalFeatures([]);
    setNominalMappings({});
    setOrdinalMappings({});
    setShowTable(false);
  };

  /* ================================ RENDER ============================== */
  return (
    <div className="mx-4 max-w-7xl">
      {/* Nominal Encoding Section */}
      <div className="bg-white rounded-xl shadow p-6 mb-6">
        <h2 className="text-xl font-bold text-blue-700 mb-4">
          Nominal Feature Encoding
        </h2>
        <Dropdown
          options={nominalEncodingOptions}
          placeholder="Select method"
          tell="Select method for encoding nominal features:"
          searchPlaceholder="Search encoding method..."
          value={nominalMethod}
          setValue={setNominalMethod}
        />

        {nominalMethod === "One-Hot encoding" && (
          <div className="mt-6 space-y-3">
            <label className="inline-flex items-center space-x-2">
              <input
                type="checkbox"
                className="form-checkbox text-blue-600"
                checked={dropFirstCategory}
                onChange={(e) => setDropFirstCategory(e.target.checked)}
              />
              <span className="text-sm text-gray-700">
                Drop First Category to avoid multicollinearity
              </span>
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {nominalFeatures.map((f) => (
                <div
                  key={f.id}
                  className="bg-gray-50 rounded-lg p-4 shadow flex flex-col gap-2"
                >
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id={`nominal-${f.id}`}
                      className="accent-blue-500"
                      checked={selectedNominalFeatures.includes(f.id)}
                      onChange={() => toggleNominal(f.id)}
                    />
                    <span className="font-medium">{f.feature}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">
                      Dummy Column Prefix:
                    </span>
                    <input
                      type="text"
                      value={dummyPrefix[f.id] || ""}
                      onChange={(e) => handlePrefixChange(f.id, e.target.value)}
                      placeholder={`${f.feature}_`}
                      className="px-2 py-1 border border-gray-300 rounded-md text-sm focus:ring focus:ring-blue-300"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Ordinal Encoding Section */}
      <div className="bg-white rounded-xl shadow p-6 mb-6">
        <h2 className="text-xl font-bold text-blue-700 mb-4">
          Ordinal Feature Encoding
        </h2>
        <Dropdown
          options={ordinalEncodingOptions}
          placeholder="Select method"
          tell="Select method for encoding ordinal features:"
          searchPlaceholder="Search encoding method..."
          value={ordinalMethod}
          setValue={setOrdinalMethod}
        />

        {ordinalMethod === "Ordinal encoding" && (
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            {ordinalFeatures.map((f) => (
              <div
                key={f.id}
                className="bg-gray-50 rounded-lg p-4 shadow flex items-center gap-2"
              >
                <input
                  type="checkbox"
                  id={`ordinal-${f.id}`}
                  className="accent-blue-500"
                  checked={selectedOrdinalFeatures.includes(f.id)}
                  onChange={() => toggleOrdinal(f.id)}
                />
                <span className="font-medium">{f.feature}</span>
              </div>
            ))}
          </div>
        )}
      </div>

     {/* Apply and Reset buttons */}
        <div className="mt-6 flex gap-4 mb-6">
          <button
            onClick={handleApply}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-300"
          >
            Apply Changes
          </button>
          <button
            onClick={handleResetParameters}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg font-semibold hover:bg-gray-700 transition-colors duration-300"
          >
            Reset to Default
          </button>
        </div>

      {showTable && (
        <ApplyTable
          originalData={dataset}
          cleanedData={dataset}
          tab="Encoding"
        />
      )}
    </div>
  );
};

export default Encoding;
