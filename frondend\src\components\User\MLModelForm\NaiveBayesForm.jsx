import React, { useState, useEffect } from "react";

const NaiveBayesForm = ({ onParamsChange }) => {
    const [params, setParams] = useState({
        alpha: 0,
        fit_prior: true,
        class_prior: [0.7, 0.3],
    });

    useEffect(() => {
        onParamsChange(params);
    }, [params, onParamsChange]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setParams((prevParams) => ({
            ...prevParams,
            [name]: value,
        }));
    };

    {/*     const handleChange = (e) => {
        const { name, value } = e.target;

        setParams((prevParams) => ({
            ...prevParams,
            [name]: name === "alpha" ? parseFloat(value) :
                    name === "fit_prior" ? value === "true" : 
                    name === "class_prior" ? value.split(",").map((v) => parseFloat(v.trim())) :
                    value,
        }));

        onParamsChange(params);
    };*/ }

    return (
        <div className="space-y-4">
            <div>
                <label className="block text-sm font-medium">Alpha:</label>
                <input
                    type="number"
                    name="alpha"
                    value={params.alpha}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="0.1"
                />
            </div>

            <div>
                <label className="block text-sm font-medium">Fit Prior:</label>
                <select
                    name="fit_prior"
                    value={params.fit_prior}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="true">True</option>
                    <option value="false">False</option>
                </select>
            </div>

            <div>
                <label className="block text-sm font-medium">Class Prior:</label>
                <input
                    type="text"
                    name="class_prior"
                    value={params.class_prior}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    placeholder="e.g., 0.7, 0.3"
                />
            </div>
        </div>
    );
};

export default NaiveBayesForm;