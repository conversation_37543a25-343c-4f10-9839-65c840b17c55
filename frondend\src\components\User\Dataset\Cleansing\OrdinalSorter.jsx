import React, { useState, useEffect } from "react";
import { motion, Reorder } from "framer-motion"; // จาก framer-motion สำหรับทำ drag-and-drop animation
import { GripVertical } from "lucide-react"; // ไอคอนรูป “grip” จากไลบรารี lucide-react
import { useSelector, useDispatch } from "react-redux";
import { updateOrdinalOrder } from "../../../../app/features/progress/upload/uploadSlice"; // adjust the import path

/**
 * OrdinalSorter component for interactively sorting ordinal feature values.
 *
 * Renders draggable lists for each ordinal feature, allowing users to reorder the unique values.
 * The new order is dispatched to Redux using the updateOrdinalOrder action.
 *
 * @component
 * @param {Object} props - Component props
 * @param {string} [props.title="Sort Ordinal Feature"] - Title displayed above each feature sorter
 * @param {string} [props.cardClassName] - CSS classes for the card container
 * @param {string} [props.titleClassName] - CSS classes for the title
 * @param {string} [props.itemClassName] - CSS classes for each sortable item
 * @returns {JSX.Element}
 *
 * @example
 * <OrdinalSorter
 *   title="Sort Education Levels"
 *   cardClassName="max-w-md"
 *   titleClassName="text-lg"
 *   itemClassName="bg-blue-500"
 * />
 *
 * Redux State Dependencies:
 * - state.upload.features.ordinal.items: Array of ordinal feature objects, each with:
 *   - feature: string (feature name)
 *   - uniqueValue: string[] (array of unique values for the feature)
 *
 * Dispatches:
 * - updateOrdinalOrder({ feature, newOrder }): Updates the order of unique values for a feature in Redux.
 */
const OrdinalSorter = ({
  title = "Sort Ordinal Feature",
  cardClassName = "max-w-max bg-white shadow-md rounded-lg p-6",
  titleClassName = "text-xl font-bold text-center mb-4",
  itemClassName = "bg-blue-500 text-white rounded-lg mb-3 cursor-move",
}) => {
  const dispatch = useDispatch();
  const ordinalItems = useSelector(
    (state) => state.upload.features.ordinal.items
  );
  /**
  * Redux selector to retrieve ordinal feature list.
  * Each item is expected to contain:
  * - feature: string (feature name)
  * - uniqueValue: string[] (unique values for that feature)
  */
  // console.log("Ordinal Items:", ordinalItems); // Log the ordinal items for debugging
  const [orders, setOrders] = useState(
    ordinalItems.map((item) => item.uniqueValue || [])
  );
  /**
  * Local state to track current sort order for each ordinal feature.
  * Synced with Redux upon drag-end.
  *
  * @type {string[][]} Array of string arrays, each representing one feature’s ordered values
  */

  useEffect(() => {
    setOrders(ordinalItems.map((item) => item.uniqueValue || []));
  }, [ordinalItems]);
  /**
  * Sync local `orders` state with Redux data when ordinalItems change.
  * Helps reflect updates from outside (e.g., after reset or data load).
  */

  const handleReorder = (feature, newOrder, idx) => {
    /**
    * Handler when user reorders a feature’s unique values.
    *
    * @param {string} feature - The feature name being reordered
    * @param {string[]} newOrder - The updated order of unique values
    * @param {number} idx - Index of the feature in the `orders` array
    */
    const newOrders = [...orders];
    newOrders[idx] = newOrder;
    setOrders(newOrders);
    dispatch(
      updateOrdinalOrder({
        feature,
        newOrder,
      })
    );
  };

  return (
    <div className="grid grid-cols-4 gap-4">
      {ordinalItems.map((item, idx) => (
        <div key={item.feature || "empty"} className={cardClassName}>
          {title && <h2 className={titleClassName}>{title}</h2>}
          <h2 className={titleClassName}>
            {item.feature || "No ordinal feature selected"}
          </h2>
          {/* ใช้ Reorder.Group จาก framer-motion เพื่อจัดเรียงแนวตั้ง */}
          {/* values คือค่าปัจจุบัน */}
          {/* onReorder → เมื่อ drag แล้วปล่อย จะเรียก handleReorder */}
          {item && Array.isArray(item.uniqueValue) ? (
            <Reorder.Group
              axis="y"
              values={orders[idx] || []}
              onReorder={(newOrder) =>
                handleReorder(item.feature, newOrder, idx)
              }
              className="space-y-3"
            >
              {/* แสดงแต่ละค่าที่ reorder ได้ */}
              {/* มีไอคอน Grip และข้อความในกล่องแบบ drag-and-drop */}
              {/* ใส่ motion.div เพื่อให้มี effect (tap/hover) */}
              {(orders[idx] || []).map((value) => (
                <Reorder.Item
                  key={value}
                  value={value}
                  className="focus-visible:outline-none"
                >
                  <motion.div
                    className={itemClassName}
                    whileTap={{ scale: 1.02 }}
                    whileHover={{ scale: 1.01 }}
                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                  >
                    <div className="flex items-center p-4">
                      <GripVertical className="mr-2 h-5 w-5 flex-shrink-0 opacity-50" />
                      <span className="text-lg">{value}</span>
                    </div>
                  </motion.div>
                </Reorder.Item>
              ))}
            </Reorder.Group>
          ) : (
            <p className="text-gray-500">No ordinal values to sort.</p>
          )}
        </div>
      ))}
    </div>
  );
};

export default OrdinalSorter;
