import {
  __commonJS
} from "./chunk-DC5AMYBS.js";

// node_modules/density-clustering/lib/DBSCAN.js
var require_DBSCAN = __commonJS({
  "node_modules/density-clustering/lib/DBSCAN.js"(exports, module) {
    function DBSCAN(dataset, epsilon, minPts, distanceFunction) {
      this.dataset = [];
      this.epsilon = 1;
      this.minPts = 2;
      this.distance = this._euclideanDistance;
      this.clusters = [];
      this.noise = [];
      this._visited = [];
      this._assigned = [];
      this._datasetLength = 0;
      this._init(dataset, epsilon, minPts, distanceFunction);
    }
    DBSCAN.prototype.run = function(dataset, epsilon, minPts, distanceFunction) {
      this._init(dataset, epsilon, minPts, distanceFunction);
      for (var pointId = 0; pointId < this._datasetLength; pointId++) {
        if (this._visited[pointId] !== 1) {
          this._visited[pointId] = 1;
          var neighbors = this._regionQuery(pointId);
          if (neighbors.length < this.minPts) {
            this.noise.push(pointId);
          } else {
            var clusterId = this.clusters.length;
            this.clusters.push([]);
            this._addToCluster(pointId, clusterId);
            this._expandCluster(clusterId, neighbors);
          }
        }
      }
      return this.clusters;
    };
    DBSCAN.prototype._init = function(dataset, epsilon, minPts, distance) {
      if (dataset) {
        if (!(dataset instanceof Array)) {
          throw Error("Dataset must be of type array, " + typeof dataset + " given");
        }
        this.dataset = dataset;
        this.clusters = [];
        this.noise = [];
        this._datasetLength = dataset.length;
        this._visited = new Array(this._datasetLength);
        this._assigned = new Array(this._datasetLength);
      }
      if (epsilon) {
        this.epsilon = epsilon;
      }
      if (minPts) {
        this.minPts = minPts;
      }
      if (distance) {
        this.distance = distance;
      }
    };
    DBSCAN.prototype._expandCluster = function(clusterId, neighbors) {
      for (var i = 0; i < neighbors.length; i++) {
        var pointId2 = neighbors[i];
        if (this._visited[pointId2] !== 1) {
          this._visited[pointId2] = 1;
          var neighbors2 = this._regionQuery(pointId2);
          if (neighbors2.length >= this.minPts) {
            neighbors = this._mergeArrays(neighbors, neighbors2);
          }
        }
        if (this._assigned[pointId2] !== 1) {
          this._addToCluster(pointId2, clusterId);
        }
      }
    };
    DBSCAN.prototype._addToCluster = function(pointId, clusterId) {
      this.clusters[clusterId].push(pointId);
      this._assigned[pointId] = 1;
    };
    DBSCAN.prototype._regionQuery = function(pointId) {
      var neighbors = [];
      for (var id = 0; id < this._datasetLength; id++) {
        var dist = this.distance(this.dataset[pointId], this.dataset[id]);
        if (dist < this.epsilon) {
          neighbors.push(id);
        }
      }
      return neighbors;
    };
    DBSCAN.prototype._mergeArrays = function(a, b) {
      var len = b.length;
      for (var i = 0; i < len; i++) {
        var P = b[i];
        if (a.indexOf(P) < 0) {
          a.push(P);
        }
      }
      return a;
    };
    DBSCAN.prototype._euclideanDistance = function(p, q) {
      var sum = 0;
      var i = Math.min(p.length, q.length);
      while (i--) {
        sum += (p[i] - q[i]) * (p[i] - q[i]);
      }
      return Math.sqrt(sum);
    };
    if (typeof module !== "undefined" && module.exports) {
      module.exports = DBSCAN;
    }
  }
});

// node_modules/density-clustering/lib/KMEANS.js
var require_KMEANS = __commonJS({
  "node_modules/density-clustering/lib/KMEANS.js"(exports, module) {
    function KMEANS(dataset, k, distance) {
      this.k = 3;
      this.dataset = [];
      this.assignments = [];
      this.centroids = [];
      this.init(dataset, k, distance);
    }
    KMEANS.prototype.init = function(dataset, k, distance) {
      this.assignments = [];
      this.centroids = [];
      if (typeof dataset !== "undefined") {
        this.dataset = dataset;
      }
      if (typeof k !== "undefined") {
        this.k = k;
      }
      if (typeof distance !== "undefined") {
        this.distance = distance;
      }
    };
    KMEANS.prototype.run = function(dataset, k) {
      this.init(dataset, k);
      var len = this.dataset.length;
      for (var i = 0; i < this.k; i++) {
        this.centroids[i] = this.randomCentroid();
      }
      var change = true;
      while (change) {
        change = this.assign();
        for (var centroidId = 0; centroidId < this.k; centroidId++) {
          var mean = new Array(maxDim);
          var count = 0;
          for (var dim = 0; dim < maxDim; dim++) {
            mean[dim] = 0;
          }
          for (var j = 0; j < len; j++) {
            var maxDim = this.dataset[j].length;
            if (centroidId === this.assignments[j]) {
              for (var dim = 0; dim < maxDim; dim++) {
                mean[dim] += this.dataset[j][dim];
              }
              count++;
            }
          }
          if (count > 0) {
            for (var dim = 0; dim < maxDim; dim++) {
              mean[dim] /= count;
            }
            this.centroids[centroidId] = mean;
          } else {
            this.centroids[centroidId] = this.randomCentroid();
            change = true;
          }
        }
      }
      return this.getClusters();
    };
    KMEANS.prototype.randomCentroid = function() {
      var maxId = this.dataset.length - 1;
      var centroid;
      var id;
      do {
        id = Math.round(Math.random() * maxId);
        centroid = this.dataset[id];
      } while (this.centroids.indexOf(centroid) >= 0);
      return centroid;
    };
    KMEANS.prototype.assign = function() {
      var change = false;
      var len = this.dataset.length;
      var closestCentroid;
      for (var i = 0; i < len; i++) {
        closestCentroid = this.argmin(this.dataset[i], this.centroids, this.distance);
        if (closestCentroid != this.assignments[i]) {
          this.assignments[i] = closestCentroid;
          change = true;
        }
      }
      return change;
    };
    KMEANS.prototype.getClusters = function() {
      var clusters = new Array(this.k);
      var centroidId;
      for (var pointId = 0; pointId < this.assignments.length; pointId++) {
        centroidId = this.assignments[pointId];
        if (typeof clusters[centroidId] === "undefined") {
          clusters[centroidId] = [];
        }
        clusters[centroidId].push(pointId);
      }
      return clusters;
    };
    KMEANS.prototype.argmin = function(point, set, f) {
      var min = Number.MAX_VALUE;
      var arg = 0;
      var len = set.length;
      var d;
      for (var i = 0; i < len; i++) {
        d = f(point, set[i]);
        if (d < min) {
          min = d;
          arg = i;
        }
      }
      return arg;
    };
    KMEANS.prototype.distance = function(p, q) {
      var sum = 0;
      var i = Math.min(p.length, q.length);
      while (i--) {
        var diff = p[i] - q[i];
        sum += diff * diff;
      }
      return Math.sqrt(sum);
    };
    if (typeof module !== "undefined" && module.exports) {
      module.exports = KMEANS;
    }
  }
});

// node_modules/density-clustering/lib/PriorityQueue.js
var require_PriorityQueue = __commonJS({
  "node_modules/density-clustering/lib/PriorityQueue.js"(exports, module) {
    function PriorityQueue(elements, priorities, sorting) {
      this._queue = [];
      this._priorities = [];
      this._sorting = "desc";
      this._init(elements, priorities, sorting);
    }
    PriorityQueue.prototype.insert = function(ele, priority) {
      var indexToInsert = this._queue.length;
      var index = indexToInsert;
      while (index--) {
        var priority2 = this._priorities[index];
        if (this._sorting === "desc") {
          if (priority > priority2) {
            indexToInsert = index;
          }
        } else {
          if (priority < priority2) {
            indexToInsert = index;
          }
        }
      }
      this._insertAt(ele, priority, indexToInsert);
    };
    PriorityQueue.prototype.remove = function(ele) {
      var index = this._queue.length;
      while (index--) {
        var ele2 = this._queue[index];
        if (ele === ele2) {
          this._queue.splice(index, 1);
          this._priorities.splice(index, 1);
          break;
        }
      }
    };
    PriorityQueue.prototype.forEach = function(func) {
      this._queue.forEach(func);
    };
    PriorityQueue.prototype.getElements = function() {
      return this._queue;
    };
    PriorityQueue.prototype.getElementPriority = function(index) {
      return this._priorities[index];
    };
    PriorityQueue.prototype.getPriorities = function() {
      return this._priorities;
    };
    PriorityQueue.prototype.getElementsWithPriorities = function() {
      var result = [];
      for (var i = 0, l = this._queue.length; i < l; i++) {
        result.push([this._queue[i], this._priorities[i]]);
      }
      return result;
    };
    PriorityQueue.prototype._init = function(elements, priorities, sorting) {
      if (elements && priorities) {
        this._queue = [];
        this._priorities = [];
        if (elements.length !== priorities.length) {
          throw new Error("Arrays must have the same length");
        }
        for (var i = 0; i < elements.length; i++) {
          this.insert(elements[i], priorities[i]);
        }
      }
      if (sorting) {
        this._sorting = sorting;
      }
    };
    PriorityQueue.prototype._insertAt = function(ele, priority, index) {
      if (this._queue.length === index) {
        this._queue.push(ele);
        this._priorities.push(priority);
      } else {
        this._queue.splice(index, 0, ele);
        this._priorities.splice(index, 0, priority);
      }
    };
    if (typeof module !== "undefined" && module.exports) {
      module.exports = PriorityQueue;
    }
  }
});

// node_modules/density-clustering/lib/OPTICS.js
var require_OPTICS = __commonJS({
  "node_modules/density-clustering/lib/OPTICS.js"(exports, module) {
    if (typeof module !== "undefined" && module.exports) {
      PriorityQueue = require_PriorityQueue();
    }
    var PriorityQueue;
    function OPTICS(dataset, epsilon, minPts, distanceFunction) {
      this.epsilon = 1;
      this.minPts = 1;
      this.distance = this._euclideanDistance;
      this._reachability = [];
      this._processed = [];
      this._coreDistance = 0;
      this._orderedList = [];
      this._init(dataset, epsilon, minPts, distanceFunction);
    }
    OPTICS.prototype.run = function(dataset, epsilon, minPts, distanceFunction) {
      this._init(dataset, epsilon, minPts, distanceFunction);
      for (var pointId = 0, l = this.dataset.length; pointId < l; pointId++) {
        if (this._processed[pointId] !== 1) {
          this._processed[pointId] = 1;
          this.clusters.push([pointId]);
          var clusterId = this.clusters.length - 1;
          this._orderedList.push(pointId);
          var priorityQueue = new PriorityQueue(null, null, "asc");
          var neighbors = this._regionQuery(pointId);
          if (this._distanceToCore(pointId) !== void 0) {
            this._updateQueue(pointId, neighbors, priorityQueue);
            this._expandCluster(clusterId, priorityQueue);
          }
        }
      }
      return this.clusters;
    };
    OPTICS.prototype.getReachabilityPlot = function() {
      var reachabilityPlot = [];
      for (var i = 0, l = this._orderedList.length; i < l; i++) {
        var pointId = this._orderedList[i];
        var distance = this._reachability[pointId];
        reachabilityPlot.push([pointId, distance]);
      }
      return reachabilityPlot;
    };
    OPTICS.prototype._init = function(dataset, epsilon, minPts, distance) {
      if (dataset) {
        if (!(dataset instanceof Array)) {
          throw Error("Dataset must be of type array, " + typeof dataset + " given");
        }
        this.dataset = dataset;
        this.clusters = [];
        this._reachability = new Array(this.dataset.length);
        this._processed = new Array(this.dataset.length);
        this._coreDistance = 0;
        this._orderedList = [];
      }
      if (epsilon) {
        this.epsilon = epsilon;
      }
      if (minPts) {
        this.minPts = minPts;
      }
      if (distance) {
        this.distance = distance;
      }
    };
    OPTICS.prototype._updateQueue = function(pointId, neighbors, queue) {
      var self = this;
      this._coreDistance = this._distanceToCore(pointId);
      neighbors.forEach(function(pointId2) {
        if (self._processed[pointId2] === void 0) {
          var dist = self.distance(self.dataset[pointId], self.dataset[pointId2]);
          var newReachableDistance = Math.max(self._coreDistance, dist);
          if (self._reachability[pointId2] === void 0) {
            self._reachability[pointId2] = newReachableDistance;
            queue.insert(pointId2, newReachableDistance);
          } else {
            if (newReachableDistance < self._reachability[pointId2]) {
              self._reachability[pointId2] = newReachableDistance;
              queue.remove(pointId2);
              queue.insert(pointId2, newReachableDistance);
            }
          }
        }
      });
    };
    OPTICS.prototype._expandCluster = function(clusterId, queue) {
      var queueElements = queue.getElements();
      for (var p = 0, l = queueElements.length; p < l; p++) {
        var pointId = queueElements[p];
        if (this._processed[pointId] === void 0) {
          var neighbors = this._regionQuery(pointId);
          this._processed[pointId] = 1;
          this.clusters[clusterId].push(pointId);
          this._orderedList.push(pointId);
          if (this._distanceToCore(pointId) !== void 0) {
            this._updateQueue(pointId, neighbors, queue);
            this._expandCluster(clusterId, queue);
          }
        }
      }
    };
    OPTICS.prototype._distanceToCore = function(pointId) {
      var l = this.epsilon;
      for (var coreDistCand = 0; coreDistCand < l; coreDistCand++) {
        var neighbors = this._regionQuery(pointId, coreDistCand);
        if (neighbors.length >= this.minPts) {
          return coreDistCand;
        }
      }
      return;
    };
    OPTICS.prototype._regionQuery = function(pointId, epsilon) {
      epsilon = epsilon || this.epsilon;
      var neighbors = [];
      for (var id = 0, l = this.dataset.length; id < l; id++) {
        if (this.distance(this.dataset[pointId], this.dataset[id]) < epsilon) {
          neighbors.push(id);
        }
      }
      return neighbors;
    };
    OPTICS.prototype._euclideanDistance = function(p, q) {
      var sum = 0;
      var i = Math.min(p.length, q.length);
      while (i--) {
        sum += (p[i] - q[i]) * (p[i] - q[i]);
      }
      return Math.sqrt(sum);
    };
    if (typeof module !== "undefined" && module.exports) {
      module.exports = OPTICS;
    }
  }
});

// node_modules/density-clustering/lib/index.js
var require_lib = __commonJS({
  "node_modules/density-clustering/lib/index.js"(exports, module) {
    if (typeof module !== "undefined" && module.exports) {
      module.exports = {
        DBSCAN: require_DBSCAN(),
        KMEANS: require_KMEANS(),
        OPTICS: require_OPTICS(),
        PriorityQueue: require_PriorityQueue()
      };
    }
  }
});
export default require_lib();
//# sourceMappingURL=density-clustering.js.map
