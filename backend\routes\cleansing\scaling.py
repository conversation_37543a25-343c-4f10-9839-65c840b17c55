from flask import Blueprint, request, jsonify
from db import init_db
import pandas as pd
from bson import ObjectId
from sklearn.preprocessing import StandardScaler, MinMaxScaler

# สร้าง Blueprint
scaling_bp = Blueprint('scaling', __name__)

# MongoDB
from flask import Flask
app = Flask(__name__)
mongo = init_db(app)

# --------- Apply Standard Scaling ---------
def apply_standard_scaling(df, feature_cols):
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(df[feature_cols])
    return X_scaled, scaler

# --------- Apply Min-Max Scaling ---------
def apply_minmax_scaling(df, feature_cols, feature_min, feature_max):
    scaler = MinMaxScaler(feature_range=(feature_min, feature_max))
    X_scaled = scaler.fit_transform(df[feature_cols])
    return X_scaled, scaler

# --------- Route Scaling ---------
@scaling_bp.route('/scaling', methods=['POST'])
def train_model():
    
    # รับข้อมูลจากผู้ใช้
    dataset_id = request.json.get("dataset_id")
    scaling = request.json.get("scaling")  # เลือก "standard" หรือ "minmax"
    min_value = request.json.get("min_value")
    max_value = request.json.get("max_value")
    selected_columns = request.json.get("columns")  # คอลัมน์ที่ผู้ใช้เลือก
    
    # ตรวจสอบว่ามีการส่งคอลัมน์หรือไม่
    if not selected_columns:
        return jsonify({"msg": "No columns selected for scaling"}), 400
    
    # Load current ถ้าไม่มีใช้ previous
    current_doc = mongo.db.current.find_one({"dataset_id": ObjectId(dataset_id)})
    if current_doc:
        df = pd.DataFrame(current_doc["data"])
        previous_data = current_doc["data"]
    else:
        previous_doc = mongo.db.previous.find_one({"dataset_id": ObjectId(dataset_id)})
        if not previous_doc:
            return jsonify({"msg": "Dataset not found"}), 404
        df = pd.DataFrame(previous_doc["data"])
        previous_data = previous_doc["data"]

    # ตรวจสอบคอลัมน์
    missing_cols = [col for col in selected_columns if col not in df.columns]
    if missing_cols:
        return jsonify({"msg": f"Missing columns: {', '.join(missing_cols)}"}), 400

    # กำหนด feature_cols เป็นคอลัมน์ที่ผู้ใช้เลือก
    feature_cols = selected_columns
    
    # เลือกวิธีการสเกล
    if scaling == "standard":
        X_scaled, scaler = apply_standard_scaling(df, feature_cols)
    elif scaling == "minmax":
        # ตรวจสอบ min_value และ max_value ก่อนทำการ scaling
        if min_value is None or max_value is None:
            return jsonify({"msg": "min_value and max_value are required for minmax scaling"}), 400
        X_scaled, scaler = apply_minmax_scaling(df, feature_cols, min_value, max_value)
    else:
        return jsonify({"msg": "Invalid scaling type"}), 400
    
    # แปลง X_scaled เป็น DataFrame
    X_scaled_df = pd.DataFrame(X_scaled, columns=feature_cols)

    # คัดลอกคอลัมน์ที่ไม่ได้เลือก
    other_cols = [col for col in df.columns if col not in selected_columns]
    
    # รวมข้อมูลที่ถูกสเกลและข้อมูลที่ไม่ได้สเกล โดยให้คอลัมน์ที่ไม่ได้สเกลอยู่ในลำดับเดิม
    result_df = pd.concat([X_scaled_df, df[other_cols]], axis=1)

    # จัดเรียงคอลัมน์ให้ตรงกับลำดับในฐานข้อมูล
    result_df = result_df[df.columns]  # ใช้ลำดับคอลัมน์จาก DataFrame เดิม

    # แปลงเป็น dictionary
    scaled_data = result_df.to_dict(orient='records')

    # เก็บ previous ข้อมูลก่อน scaling
    mongo.db.previous.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {
            "$set": {
                "data": previous_data,
            }
        },
        upsert=True
    )

    # อัปเดต current ด้วยข้อมูลใหม่
    mongo.db.current.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {
            "$set": {
                "data": scaled_data,
            }
        },
        upsert=True
    )

    return jsonify({
        "scaled_data": scaled_data
    }), 200