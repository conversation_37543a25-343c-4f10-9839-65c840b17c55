{"name": "react-demos", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/accessibility": "^3.1.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@tailwindcss/vite": "^4.0.13", "chart.js": "^4.4.8", "chartjs-plugin-datalabels": "^2.2.0", "framer-motion": "^12.6.3", "motion": "^12.6.2", "plotly.js-dist-min": "^3.0.1", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-router-dom": "^7.3.0", "recharts": "^2.15.1", "tailwindcss": "^4.0.13", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "vite": "^6.2.2"}}