/**
 * Machine Learning API service
 * 
 * Handles all machine learning related API calls including:
 * - Model training
 * - Predictions
 * - Model evaluation
 * - Unsupervised learning
 */

import apiService from './api';

const mlApi = {
  // Model Training
  training: {
    /**
     * Train a machine learning model
     * @param {Object} config - Training configuration
     * @returns {Promise} API response with training results
     */
    trainModel: (config) => {
      return apiService.post('/train/train-full', config);
    },

    /**
     * Get available models and their parameters
     * @returns {Promise} API response with model options
     */
    getModelOptions: () => {
      return apiService.get('/train/models');
    },

    /**
     * Get training history for a dataset
     * @param {string} datasetId - Dataset ID
     * @returns {Promise} API response with training history
     */
    getTrainingHistory: (datasetId) => {
      return apiService.get('/train/history', { dataset_id: datasetId });
    },

    /**
     * Get model evaluation metrics
     * @param {string} datasetId - Dataset ID
     * @returns {Promise} API response with evaluation metrics
     */
    getEvaluationMetrics: (datasetId) => {
      return apiService.get('/train/evaluation', { dataset_id: datasetId });
    },
  },

  // Predictions
  prediction: {
    /**
     * Get available columns for prediction input
     * @param {string} datasetId - Dataset ID
     * @returns {Promise} API response with available columns
     */
    getColumns: (datasetId) => {
      return apiService.get('/predicts/columns', { dataset_id: datasetId });
    },

    /**
     * Make a prediction using trained model
     * @param {Object} config - Prediction configuration
     * @returns {Promise} API response with prediction results
     */
    predict: (config) => {
      return apiService.post('/predicts/predict', config);
    },

    /**
     * Get prediction history
     * @param {string} datasetId - Dataset ID
     * @returns {Promise} API response with prediction history
     */
    getPredictionHistory: (datasetId) => {
      return apiService.get('/predicts/history', { dataset_id: datasetId });
    },

    /**
     * Batch prediction from file
     * @param {FormData} formData - Form data containing prediction file
     * @returns {Promise} API response with batch prediction results
     */
    batchPredict: (formData) => {
      return apiService.uploadFile('/predicts/batch', formData);
    },
  },

  // Unsupervised Learning
  unsupervised: {
    /**
     * Perform clustering analysis
     * @param {Object} config - Clustering configuration
     * @returns {Promise} API response with clustering results
     */
    cluster: (config) => {
      return apiService.post('/unsupervised/cluster', config);
    },

    /**
     * Get clustering options and recommendations
     * @param {string} datasetId - Dataset ID
     * @returns {Promise} API response with clustering options
     */
    getClusteringOptions: (datasetId) => {
      return apiService.get('/unsupervised/clustering-options', { dataset_id: datasetId });
    },

    /**
     * Perform association rule mining
     * @param {Object} config - Association rule mining configuration
     * @returns {Promise} API response with association rules
     */
    associationRules: (config) => {
      return apiService.post('/unsupervised/association-rules', config);
    },

    /**
     * Perform anomaly detection
     * @param {Object} config - Anomaly detection configuration
     * @returns {Promise} API response with anomaly detection results
     */
    anomalyDetection: (config) => {
      return apiService.post('/unsupervised/anomaly-detection', config);
    },
  },

  // Model Management
  models: {
    /**
     * Get all trained models for a dataset
     * @param {string} datasetId - Dataset ID
     * @returns {Promise} API response with models list
     */
    getModels: (datasetId) => {
      return apiService.get('/models/list', { dataset_id: datasetId });
    },

    /**
     * Get model details
     * @param {string} modelId - Model ID
     * @returns {Promise} API response with model details
     */
    getModelDetails: (modelId) => {
      return apiService.get(`/models/${modelId}`);
    },

    /**
     * Delete a trained model
     * @param {string} modelId - Model ID
     * @returns {Promise} API response
     */
    deleteModel: (modelId) => {
      return apiService.delete(`/models/${modelId}`);
    },

    /**
     * Export model
     * @param {string} modelId - Model ID
     * @returns {Promise} API response with model file
     */
    exportModel: (modelId) => {
      return apiService.downloadFile(`/models/${modelId}/export`);
    },
  },
};

export default mlApi;
