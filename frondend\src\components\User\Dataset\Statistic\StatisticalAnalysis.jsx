import React, { useState } from "react";
import StatisticsPanel from "./StatisticsPanel";
import { useSelector } from "react-redux";

/**
 * StatisticalAnalysis component provides a user interface to perform
 * basic statistical analysis on a selected column from the uploaded dataset.
 *
 * It allows users to:
 * - Select a column from the dataset
 * - View statistics such as mean, median, mode, std. deviation, etc.
 *   (via a nested `StatisticsPanel` component)
 *
 * The dataset is pulled from the Redux store (`state.upload.dataset`).
 *
 * @component
 *
 * @example
 * // Usage in parent component
 * <StatisticalAnalysis />
 */

const StatisticalAnalysis = () => {
  // State to store the selected column name
  const sampleDataset = useSelector((state) => state.upload.dataset);
  // เก็บชื่อคอลัมน์ที่ถูกเลือกโดยผู้ใช้
  const [selectedColumn, setSelectedColumn] = useState(null);

  // Handle column selection change
  const handleColumnChange = (e) => {
    setSelectedColumn(e.target.value);
  };

  return (
    <div className="p-6 bg-white shadow-md rounded-lg">
      <h2 className="text-2xl font-semibold mb-4">Statistical Analysis</h2>

      {/* Dropdown for selecting a specific column to see basic statistics */}
      <div className="mb-4">
        <label
          htmlFor="column-select"
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          Select Column for Analysis:
        </label>
        <select
          id="column-select"
          className="border border-gray-300 rounded-md p-2 w-full"
          value={selectedColumn || ""}
          onChange={handleColumnChange}
        >
          <option value="" disabled>
            -- Select a Column --
          </option>
          {/* ดึงชื่อคีย์ของ object แถวแรก (ชื่อคอลัมน์) */}
          {/* เมื่อเลือกคอลัมน์ จะเรียก handleColumnChange */}
          {Object.keys(sampleDataset[0]).map((key) => (
            <option key={key} value={key}>
              {key}
            </option>
          ))}
        </select>
      </div>

      {/* Pass the selected column and dataset to the StatisticsPanel */}
      {selectedColumn && (
        <StatisticsPanel
          dataset={sampleDataset}
          selectedColumn={selectedColumn}
        />
      )}
    </div>
  );
};

export default StatisticalAnalysis;
