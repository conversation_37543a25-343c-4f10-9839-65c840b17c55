import React from "react";
import DatasetUpload from "./DatasetUpload";

/**
 * DatasetManagement is a high-level React component responsible for managing datasets in the application.
 *
 * It acts as a wrapper layout that contains:
 * - A page heading for dataset management
 * - A section for uploading datasets using the <DatasetUpload /> component
 *
 * This component is typically used as a page or section in a data science or ML workflow app.
 *
 * @component
 *
 * @example
 * // Render inside a route
 * <Route path="/dataset-management" element={<DatasetManagement />} />
 *
 * @returns {JSX.Element} A layout with dataset management tools including upload functionality.
 */

const DatasetManagement = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Page Title */}
      <h1 className="text-3xl font-bold text-gray-800">Dataset Management</h1>

      {/* Dataset List Section */}
      <div className="bg-white shadow-md rounded-lg p-6">
        {/* Reusable DatasetTable component */}
        <DatasetUpload />
      </div>
    </div>
  );
};

export default DatasetManagement;
