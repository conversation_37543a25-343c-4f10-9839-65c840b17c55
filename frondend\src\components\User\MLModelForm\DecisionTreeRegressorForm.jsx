import React, { useState, useEffect } from "react";

const DecisionTreeRegressorForm = ({ onParamsChange }) => {
    const [params, setParams] = useState({
        max_depth: 5,
        min_samples_split: 10,
        criterion: "squared_error",
    });

    useEffect(() => {
        onParamsChange(params);
    }, [params, onParamsChange]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setParams((prevParams) => ({
            ...prevParams,
            [name]: value,
        }));
    };

    return (
        <div className="space-y-2 p-4 bg-white rounded shadow">

            <div>
                <label className="block text-sm font-medium">max_depth:</label>
                <input
                    type="number"
                    name="max_depth"
                    value={params.max_depth}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="10"
                />
            </div>

            <div>
                <label className="block text-sm font-medium">min_samples_split:</label>
                <input
                    type="number"
                    name="min_samples_split"
                    value={params.min_samples_split}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="10"
                />
            </div>

            <div>
                <label className="block text-sm font-medium">fit_intercept:</label>
                <select
                    name="fit_intercept"
                    value={params.fit_intercept}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="squared_error">squared_error</option>
                    <option value="friedman_mse">friedman_mse</option>
                    <option value="absolute_error">absolute_error</option>
                    <option value="poisson">poisson</option>
                </select>
            </div>
        </div>
    );
};

export default DecisionTreeRegressorForm;
