from flask import Blueprint, request, jsonify
from werkzeug.security import generate_password_hash, check_password_hash
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from db import init_db
from datetime import datetime
import pytz
from bson import ObjectId # เพื่อใช้งานในการค้นหา _id

auth_bp = Blueprint('auth', __name__)

# MongoDB
from flask import Flask
app = Flask(__name__)
mongo = init_db(app)

# Set timezone Bangkok
bangkok_tz = pytz.timezone('Asia/Bangkok')
def get_current_time():
    return datetime.now(bangkok_tz).strftime('%Y-%m-%dT%H:%M:%SZ')

# --------- Route Register ---------
@auth_bp.route('/register', methods=['POST'])
def register_user():
    # request
    username = request.json.get("username")
    email = request.json.get("email")
    password = request.json.get("password")
    confirm_password = request.json.get("confirm_password")
    
    if not username or not email or not password or not confirm_password:
        return jsonify({"msg": "Username, email, password, and confirm_password are required!"}), 400
    
    # ตรวจสอบว่ารหัสผ่านและการยืนยันรหัสผ่านตรงกันหรือไม่
    if password != confirm_password:
        return jsonify({"msg": "Password and confirm password do not match!"}), 400
    
    # check username
    existing_user = mongo.db.users.find_one({"username": username})
    if existing_user:
        return jsonify({"msg": "Username already exists!"}), 400
    
    # get password
    hashed_password = generate_password_hash(password)
    
    # เก็บข้อมูลผู้ใช้ลงใน MongoDB
    user_data = {
        "username": username,
        "email": email,
        "password": hashed_password,
        "role": "user",
        #"uploaded_datasets": [],
        "created_at": get_current_time(),
        "last_login": ""
    }
    
    mongo.db.users.insert_one(user_data)
    return jsonify({"msg": "User registered successfully!"}), 201

# --------- Route Login ---------
@auth_bp.route('/login', methods=['POST'])
def login():
    username = request.json.get("username")
    password = request.json.get("password")
    
    if not username or not password:
        return jsonify({"msg": "Username and password are required!"}), 400
    
    # check username
    user = mongo.db.users.find_one({"username": username})
    if not user or not check_password_hash(user['password'], password):
        return jsonify({"msg": "Invalid username or password!"}), 401
    
    # last_login
    mongo.db.users.update_one(
        {"username": username},
        {"$set": {"last_login": get_current_time()}}
    )
    
    # JWT token
    access_token = create_access_token(identity=username)
    
    # ส่งข้อมูลของผู้ใช้กลับมาพร้อมกับ JWT token
    return jsonify({
        "msg": "Login successful",
        "access_token": access_token,
        "user_data": {
            "username": user['username'],
            "email": user['email'],
            "role": user['role'],
            #"uploaded_datasets": user['uploaded_datasets'],
            "created_at": user['created_at'],
            "last_login": get_current_time()
        }
    }), 200

# --------- Route Protected Endpoint ทดสอบการเข้าถึงข้อมูลด้วย JWT token ---------
@auth_bp.route('/protected-endpoint', methods=['GET'])
@jwt_required()
def protected():
    current_user = get_jwt_identity()
    return jsonify(logged_in_as=current_user), 200

# --------- Route Logout ---------
@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    return jsonify({"msg": "Logout successful"}), 200

# --------- Route Get User Profile ---------
@auth_bp.route('/user/profile', methods=['GET'])
@jwt_required()
def get_user_profile():
    current_user = get_jwt_identity()
    user = mongo.db.users.find_one({"username": current_user})
    if user:
        return jsonify({
            "username": user["username"],
            "email": user["email"],
            "role": user["role"],
            "uploaded_datasets": user["uploaded_datasets"],
            "created_at": user["created_at"],
            "last_login": user["last_login"]
        }), 200
    return jsonify({"msg": "User not found"}), 404

# --------- Route Update User Profile ---------
@auth_bp.route('/user/update', methods=['PUT'])
@jwt_required()
def update_user_profile():
    current_user = get_jwt_identity()
    user_data = request.json
    updated_fields = {}

    if "email" in user_data:
        updated_fields["email"] = user_data["email"]
    
    if "role" in user_data:
        return jsonify({"msg": "You cannot update the role!"}), 403

    if updated_fields:
        mongo.db.users.update_one(
            {"username": current_user},
            {"$set": updated_fields}
        )
        return jsonify({"msg": "Profile updated successfully"}), 200
    return jsonify({"msg": "No valid fields to update"}), 400
