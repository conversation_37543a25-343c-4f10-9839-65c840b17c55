/**
 * Data Cleansing API service
 * 
 * Handles all data cleansing and preprocessing API calls including:
 * - Missing value handling
 * - Data scaling/normalization
 * - Encoding categorical variables
 * - Duplicate removal
 * - Outlier detection and handling
 * - Dimensionality reduction
 * - Typo correction
 */

import apiService from './api';

const cleansingApi = {
  // Missing Value Handling
  missingValue: {
    /**
     * Get missing value analysis for a dataset
     * @param {string} datasetId - Dataset ID
     * @returns {Promise} API response with missing value analysis
     */
    analyze: (datasetId) => {
      return apiService.get('/cleansing/missing-value/analyze', { dataset_id: datasetId });
    },

    /**
     * Handle missing values using specified method
     * @param {Object} config - Missing value handling configuration
     * @returns {Promise} API response
     */
    handle: (config) => {
      return apiService.post('/cleansing/missing-value/handle', config);
    },
  },

  // Data Scaling/Normalization
  scaling: {
    /**
     * Apply scaling to numerical features
     * @param {Object} config - Scaling configuration
     * @returns {Promise} API response
     */
    apply: (config) => {
      return apiService.post('/cleansing/scaling', config);
    },

    /**
     * Get scaling options and recommendations
     * @param {string} datasetId - Dataset ID
     * @returns {Promise} API response with scaling options
     */
    getOptions: (datasetId) => {
      return apiService.get('/cleansing/scaling/options', { dataset_id: datasetId });
    },
  },

  // Categorical Encoding
  encoding: {
    /**
     * Apply encoding to categorical features
     * @param {Object} config - Encoding configuration
     * @returns {Promise} API response
     */
    apply: (config) => {
      return apiService.post('/cleansing/encoding', config);
    },

    /**
     * Get encoding options for categorical features
     * @param {string} datasetId - Dataset ID
     * @returns {Promise} API response with encoding options
     */
    getOptions: (datasetId) => {
      return apiService.get('/cleansing/encoding/options', { dataset_id: datasetId });
    },
  },

  // Duplicate Handling
  duplicates: {
    /**
     * Detect duplicates in the dataset
     * @param {string} datasetId - Dataset ID
     * @returns {Promise} API response with duplicate analysis
     */
    detect: (datasetId) => {
      return apiService.get('/cleansing/duplicates/detect', { dataset_id: datasetId });
    },

    /**
     * Remove duplicates from the dataset
     * @param {Object} config - Duplicate removal configuration
     * @returns {Promise} API response
     */
    remove: (config) => {
      return apiService.post('/cleansing/duplicates/remove', config);
    },
  },

  // Outlier Detection and Handling
  outliers: {
    /**
     * Detect outliers in the dataset
     * @param {Object} config - Outlier detection configuration
     * @returns {Promise} API response with outlier analysis
     */
    detect: (config) => {
      return apiService.post('/cleansing/outliers/detect', config);
    },

    /**
     * Handle outliers using specified method
     * @param {Object} config - Outlier handling configuration
     * @returns {Promise} API response
     */
    handle: (config) => {
      return apiService.post('/cleansing/outliers/handle', config);
    },
  },

  // Dimensionality Reduction
  dimensionalityReduction: {
    /**
     * Apply dimensionality reduction
     * @param {Object} config - Dimensionality reduction configuration
     * @returns {Promise} API response
     */
    apply: (config) => {
      return apiService.post('/cleansing/dimensionality-reduction', config);
    },

    /**
     * Get dimensionality reduction options
     * @param {string} datasetId - Dataset ID
     * @returns {Promise} API response with options
     */
    getOptions: (datasetId) => {
      return apiService.get('/cleansing/dimensionality-reduction/options', { dataset_id: datasetId });
    },
  },

  // Typo Correction
  typo: {
    /**
     * Detect typos in text columns (method 1)
     * @param {Object} config - Typo detection configuration
     * @returns {Promise} API response with typo analysis
     */
    detect1: (config) => {
      return apiService.post('/cleansing/typo1/detect', config);
    },

    /**
     * Correct typos using method 1
     * @param {Object} config - Typo correction configuration
     * @returns {Promise} API response
     */
    correct1: (config) => {
      return apiService.post('/cleansing/typo1/correct', config);
    },

    /**
     * Detect typos in text columns (method 2)
     * @param {Object} config - Typo detection configuration
     * @returns {Promise} API response with typo analysis
     */
    detect2: (config) => {
      return apiService.post('/cleansing/typo2/detect', config);
    },

    /**
     * Correct typos using method 2
     * @param {Object} config - Typo correction configuration
     * @returns {Promise} API response
     */
    correct2: (config) => {
      return apiService.post('/cleansing/typo2/correct', config);
    },
  },
};

export default cleansingApi;
