{"version": 3, "sources": ["../../density-clustering/lib/DBSCAN.js", "../../density-clustering/lib/KMEANS.js", "../../density-clustering/lib/PriorityQueue.js", "../../density-clustering/lib/OPTICS.js", "../../density-clustering/lib/index.js"], "sourcesContent": ["/**\r\n * DBSCAN - Density based clustering\r\n *\r\n * <AUTHOR> <<EMAIL>>\r\n * @copyright MIT\r\n */\r\n\r\n/**\r\n * DBSCAN class construcotr\r\n * @constructor\r\n *\r\n * @param {Array} dataset\r\n * @param {number} epsilon\r\n * @param {number} minPts\r\n * @param {function} distanceFunction\r\n * @returns {DBSCAN}\r\n */\r\nfunction DBSCAN(dataset, epsilon, minPts, distanceFunction) {\r\n  /** @type {Array} */\r\n  this.dataset = [];\r\n  /** @type {number} */\r\n  this.epsilon = 1;\r\n  /** @type {number} */\r\n  this.minPts = 2;\r\n  /** @type {function} */\r\n  this.distance = this._euclideanDistance;\r\n  /** @type {Array} */\r\n  this.clusters = [];\r\n  /** @type {Array} */\r\n  this.noise = [];\r\n\r\n  // temporary variables used during computation\r\n\r\n  /** @type {Array} */\r\n  this._visited = [];\r\n  /** @type {Array} */\r\n  this._assigned = [];\r\n  /** @type {number} */\r\n  this._datasetLength = 0;\r\n\r\n  this._init(dataset, epsilon, minPts, distanceFunction);\r\n};\r\n\r\n/******************************************************************************/\r\n// public functions\r\n\r\n/**\r\n * Start clustering\r\n *\r\n * @param {Array} dataset\r\n * @param {number} epsilon\r\n * @param {number} minPts\r\n * @param {function} distanceFunction\r\n * @returns {undefined}\r\n * @access public\r\n */\r\nDBSCAN.prototype.run = function(dataset, epsilon, minPts, distanceFunction) {\r\n  this._init(dataset, epsilon, minPts, distanceFunction);\r\n\r\n  for (var pointId = 0; pointId < this._datasetLength; pointId++) {\r\n    // if point is not visited, check if it forms a cluster\r\n    if (this._visited[pointId] !== 1) {\r\n      this._visited[pointId] = 1;\r\n\r\n      // if closest neighborhood is too small to form a cluster, mark as noise\r\n      var neighbors = this._regionQuery(pointId);\r\n\r\n      if (neighbors.length < this.minPts) {\r\n        this.noise.push(pointId);\r\n      } else {\r\n        // create new cluster and add point\r\n        var clusterId = this.clusters.length;\r\n        this.clusters.push([]);\r\n        this._addToCluster(pointId, clusterId);\r\n\r\n        this._expandCluster(clusterId, neighbors);\r\n      }\r\n    }\r\n  }\r\n\r\n  return this.clusters;\r\n};\r\n\r\n/******************************************************************************/\r\n// protected functions\r\n\r\n/**\r\n * Set object properties\r\n *\r\n * @param {Array} dataset\r\n * @param {number} epsilon\r\n * @param {number} minPts\r\n * @param {function} distance\r\n * @returns {undefined}\r\n * @access protected\r\n */\r\nDBSCAN.prototype._init = function(dataset, epsilon, minPts, distance) {\r\n\r\n  if (dataset) {\r\n\r\n    if (!(dataset instanceof Array)) {\r\n      throw Error('Dataset must be of type array, ' +\r\n        typeof dataset + ' given');\r\n    }\r\n\r\n    this.dataset = dataset;\r\n    this.clusters = [];\r\n    this.noise = [];\r\n\r\n    this._datasetLength = dataset.length;\r\n    this._visited = new Array(this._datasetLength);\r\n    this._assigned = new Array(this._datasetLength);\r\n  }\r\n\r\n  if (epsilon) {\r\n    this.epsilon = epsilon;\r\n  }\r\n\r\n  if (minPts) {\r\n    this.minPts = minPts;\r\n  }\r\n\r\n  if (distance) {\r\n    this.distance = distance;\r\n  }\r\n};\r\n\r\n/**\r\n * Expand cluster to closest points of given neighborhood\r\n *\r\n * @param {number} clusterId\r\n * @param {Array} neighbors\r\n * @returns {undefined}\r\n * @access protected\r\n */\r\nDBSCAN.prototype._expandCluster = function(clusterId, neighbors) {\r\n\r\n  /**\r\n   * It's very important to calculate length of neighbors array each time,\r\n   * as the number of elements changes over time\r\n   */\r\n  for (var i = 0; i < neighbors.length; i++) {\r\n    var pointId2 = neighbors[i];\r\n\r\n    if (this._visited[pointId2] !== 1) {\r\n      this._visited[pointId2] = 1;\r\n      var neighbors2 = this._regionQuery(pointId2);\r\n\r\n      if (neighbors2.length >= this.minPts) {\r\n        neighbors = this._mergeArrays(neighbors, neighbors2);\r\n      }\r\n    }\r\n\r\n    // add to cluster\r\n    if (this._assigned[pointId2] !== 1) {\r\n      this._addToCluster(pointId2, clusterId);\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Add new point to cluster\r\n *\r\n * @param {number} pointId\r\n * @param {number} clusterId\r\n */\r\nDBSCAN.prototype._addToCluster = function(pointId, clusterId) {\r\n  this.clusters[clusterId].push(pointId);\r\n  this._assigned[pointId] = 1;\r\n};\r\n\r\n/**\r\n * Find all neighbors around given point\r\n *\r\n * @param {number} pointId,\r\n * @param {number} epsilon\r\n * @returns {Array}\r\n * @access protected\r\n */\r\nDBSCAN.prototype._regionQuery = function(pointId) {\r\n  var neighbors = [];\r\n\r\n  for (var id = 0; id < this._datasetLength; id++) {\r\n    var dist = this.distance(this.dataset[pointId], this.dataset[id]);\r\n    if (dist < this.epsilon) {\r\n      neighbors.push(id);\r\n    }\r\n  }\r\n\r\n  return neighbors;\r\n};\r\n\r\n/******************************************************************************/\r\n// helpers\r\n\r\n/**\r\n * @param {Array} a\r\n * @param {Array} b\r\n * @returns {Array}\r\n * @access protected\r\n */\r\nDBSCAN.prototype._mergeArrays = function(a, b) {\r\n  var len = b.length;\r\n\r\n  for (var i = 0; i < len; i++) {\r\n    var P = b[i];\r\n    if (a.indexOf(P) < 0) {\r\n      a.push(P);\r\n    }\r\n  }\r\n\r\n  return a;\r\n};\r\n\r\n/**\r\n * Calculate euclidean distance in multidimensional space\r\n *\r\n * @param {Array} p\r\n * @param {Array} q\r\n * @returns {number}\r\n * @access protected\r\n */\r\nDBSCAN.prototype._euclideanDistance = function(p, q) {\r\n  var sum = 0;\r\n  var i = Math.min(p.length, q.length);\r\n\r\n  while (i--) {\r\n    sum += (p[i] - q[i]) * (p[i] - q[i]);\r\n  }\r\n\r\n  return Math.sqrt(sum);\r\n};\r\n\r\nif (typeof module !== 'undefined' && module.exports) {\r\n  module.exports = DBSCAN;\r\n}\r\n", "﻿/**\r\n * KMEANS clustering\r\n *\r\n * <AUTHOR> <<EMAIL>>\r\n * @copyright MIT\r\n */\r\n\r\n/**\r\n * KMEANS class constructor\r\n * @constructor\r\n *\r\n * @param {Array} dataset\r\n * @param {number} k - number of clusters\r\n * @param {function} distance - distance function\r\n * @returns {KMEANS}\r\n */\r\n function KMEANS(dataset, k, distance) {\r\n  this.k = 3; // number of clusters\r\n  this.dataset = []; // set of feature vectors\r\n  this.assignments = []; // set of associated clusters for each feature vector\r\n  this.centroids = []; // vectors for our clusters\r\n\r\n  this.init(dataset, k, distance);\r\n}\r\n\r\n/**\r\n * @returns {undefined}\r\n */\r\nKMEANS.prototype.init = function(dataset, k, distance) {\r\n  this.assignments = [];\r\n  this.centroids = [];\r\n\r\n  if (typeof dataset !== 'undefined') {\r\n    this.dataset = dataset;\r\n  }\r\n\r\n  if (typeof k !== 'undefined') {\r\n    this.k = k;\r\n  }\r\n\r\n  if (typeof distance !== 'undefined') {\r\n    this.distance = distance;\r\n  }\r\n};\r\n\r\n/**\r\n * @returns {undefined}\r\n */\r\nKMEANS.prototype.run = function(dataset, k) {\r\n  this.init(dataset, k);\r\n\r\n  var len = this.dataset.length;\r\n\r\n  // initialize centroids\r\n  for (var i = 0; i < this.k; i++) {\r\n    this.centroids[i] = this.randomCentroid();\r\n\t}\r\n\r\n  var change = true;\r\n  while(change) {\r\n\r\n    // assign feature vectors to clusters\r\n    change = this.assign();\r\n\r\n    // adjust location of centroids\r\n    for (var centroidId = 0; centroidId < this.k; centroidId++) {\r\n      var mean = new Array(maxDim);\r\n      var count = 0;\r\n\r\n      // init mean vector\r\n      for (var dim = 0; dim < maxDim; dim++) {\r\n        mean[dim] = 0;\r\n      }\r\n\r\n      for (var j = 0; j < len; j++) {\r\n        var maxDim = this.dataset[j].length;\r\n\r\n        // if current cluster id is assigned to point\r\n        if (centroidId === this.assignments[j]) {\r\n          for (var dim = 0; dim < maxDim; dim++) {\r\n            mean[dim] += this.dataset[j][dim];\r\n          }\r\n          count++;\r\n        }\r\n      }\r\n\r\n      if (count > 0) {\r\n        // if cluster contain points, adjust centroid position\r\n        for (var dim = 0; dim < maxDim; dim++) {\r\n          mean[dim] /= count;\r\n        }\r\n        this.centroids[centroidId] = mean;\r\n      } else {\r\n        // if cluster is empty, generate new random centroid\r\n        this.centroids[centroidId] = this.randomCentroid();\r\n        change = true;\r\n      }\r\n    }\r\n  }\r\n\r\n  return this.getClusters();\r\n};\r\n\r\n/**\r\n * Generate random centroid\r\n *\r\n * @returns {Array}\r\n */\r\nKMEANS.prototype.randomCentroid = function() {\r\n  var maxId = this.dataset.length -1;\r\n  var centroid;\r\n  var id;\r\n\r\n  do {\r\n    id = Math.round(Math.random() * maxId);\r\n    centroid = this.dataset[id];\r\n  } while (this.centroids.indexOf(centroid) >= 0);\r\n\r\n  return centroid;\r\n}\r\n\r\n/**\r\n * Assign points to clusters\r\n *\r\n * @returns {boolean}\r\n */\r\nKMEANS.prototype.assign = function() {\r\n  var change = false;\r\n  var len = this.dataset.length;\r\n  var closestCentroid;\r\n\r\n  for (var i = 0; i < len; i++) {\r\n    closestCentroid = this.argmin(this.dataset[i], this.centroids, this.distance);\r\n\r\n    if (closestCentroid != this.assignments[i]) {\r\n      this.assignments[i] = closestCentroid;\r\n      change = true;\r\n    }\r\n  }\r\n\r\n  return change;\r\n}\r\n\r\n/**\r\n * Extract information about clusters\r\n *\r\n * @returns {undefined}\r\n */\r\nKMEANS.prototype.getClusters = function() {\r\n  var clusters = new Array(this.k);\r\n  var centroidId;\r\n\r\n  for (var pointId = 0; pointId < this.assignments.length; pointId++) {\r\n    centroidId = this.assignments[pointId];\r\n\r\n    // init empty cluster\r\n    if (typeof clusters[centroidId] === 'undefined') {\r\n      clusters[centroidId] = [];\r\n    }\r\n\r\n    clusters[centroidId].push(pointId);\r\n  }\r\n\r\n  return clusters;\r\n};\r\n\r\n// utils\r\n\r\n/**\r\n * @params {Array} point\r\n * @params {Array.<Array>} set\r\n * @params {Function} f\r\n * @returns {number}\r\n */\r\nKMEANS.prototype.argmin = function(point, set, f) {\r\n  var min = Number.MAX_VALUE;\r\n  var arg = 0;\r\n  var len = set.length;\r\n  var d;\r\n\r\n  for (var i = 0; i < len; i++) {\r\n    d = f(point, set[i]);\r\n    if (d < min) {\r\n      min = d;\r\n      arg = i;\r\n    }\r\n  }\r\n\r\n  return arg;\r\n};\r\n\r\n/**\r\n * Euclidean distance\r\n *\r\n * @params {number} p\r\n * @params {number} q\r\n * @returns {number}\r\n */\r\nKMEANS.prototype.distance = function(p, q) {\r\n  var sum = 0;\r\n  var i = Math.min(p.length, q.length);\r\n\r\n  while (i--) {\r\n    var diff = p[i] - q[i];\r\n    sum += diff * diff;\r\n  }\r\n\r\n  return Math.sqrt(sum);\r\n};\r\n\r\nif (typeof module !== 'undefined' && module.exports) {\r\n  module.exports = KMEANS;\r\n}\r\n", "/**\r\n * PriorityQueue\r\n * Elements in this queue are sorted according to their value\r\n *\r\n * <AUTHOR> <<EMAIL>>\r\n * @copyright MIT\r\n */\r\n\r\n/**\r\n * PriorityQueue class construcotr\r\n * @constructor\r\n *\r\n * @example\r\n * queue: [1,2,3,4]\r\n * priorities: [4,1,2,3]\r\n * > result = [1,4,2,3]\r\n *\r\n * @param {Array} elements\r\n * @param {Array} priorities\r\n * @param {string} sorting - asc / desc\r\n * @returns {PriorityQueue}\r\n */\r\nfunction PriorityQueue(elements, priorities, sorting) {\r\n  /** @type {Array} */\r\n  this._queue = [];\r\n  /** @type {Array} */\r\n  this._priorities = [];\r\n  /** @type {string} */\r\n  this._sorting = 'desc';\r\n\r\n  this._init(elements, priorities, sorting);\r\n};\r\n\r\n/**\r\n * Insert element\r\n *\r\n * @param {Object} ele\r\n * @param {Object} priority\r\n * @returns {undefined}\r\n * @access public\r\n */\r\nPriorityQueue.prototype.insert = function(ele, priority) {\r\n  var indexToInsert = this._queue.length;\r\n  var index = indexToInsert;\r\n\r\n  while (index--) {\r\n    var priority2 = this._priorities[index];\r\n    if (this._sorting === 'desc') {\r\n      if (priority > priority2) {\r\n        indexToInsert = index;\r\n      }\r\n    } else {\r\n      if (priority < priority2) {\r\n        indexToInsert = index;\r\n      }\r\n    }\r\n  }\r\n\r\n  this._insertAt(ele, priority, indexToInsert);\r\n};\r\n\r\n/**\r\n * Remove element\r\n *\r\n * @param {Object} ele\r\n * @returns {undefined}\r\n * @access public\r\n */\r\nPriorityQueue.prototype.remove = function(ele) {\r\n  var index = this._queue.length;\r\n\r\n  while (index--) {\r\n    var ele2 = this._queue[index];\r\n    if (ele === ele2) {\r\n      this._queue.splice(index, 1);\r\n      this._priorities.splice(index, 1);\r\n      break;\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * For each loop wrapper\r\n *\r\n * @param {function} func\r\n * @returs {undefined}\r\n * @access public\r\n */\r\nPriorityQueue.prototype.forEach = function(func) {\r\n  this._queue.forEach(func);\r\n};\r\n\r\n/**\r\n * @returns {Array}\r\n * @access public\r\n */\r\nPriorityQueue.prototype.getElements = function() {\r\n  return this._queue;\r\n};\r\n\r\n/**\r\n * @param {number} index\r\n * @returns {Object}\r\n * @access public\r\n */\r\nPriorityQueue.prototype.getElementPriority = function(index) {\r\n  return this._priorities[index];\r\n};\r\n\r\n/**\r\n * @returns {Array}\r\n * @access public\r\n */\r\nPriorityQueue.prototype.getPriorities = function() {\r\n  return this._priorities;\r\n};\r\n\r\n/**\r\n * @returns {Array}\r\n * @access public\r\n */\r\nPriorityQueue.prototype.getElementsWithPriorities = function() {\r\n  var result = [];\r\n\r\n  for (var i = 0, l = this._queue.length; i < l; i++) {\r\n    result.push([this._queue[i], this._priorities[i]]);\r\n  }\r\n\r\n  return result;\r\n};\r\n\r\n/**\r\n * Set object properties\r\n *\r\n * @param {Array} elements\r\n * @param {Array} priorities\r\n * @returns {undefined}\r\n * @access protected\r\n */\r\nPriorityQueue.prototype._init = function(elements, priorities, sorting) {\r\n\r\n  if (elements && priorities) {\r\n    this._queue = [];\r\n    this._priorities = [];\r\n\r\n    if (elements.length !== priorities.length) {\r\n      throw new Error('Arrays must have the same length');\r\n    }\r\n\r\n    for (var i = 0; i < elements.length; i++) {\r\n      this.insert(elements[i], priorities[i]);\r\n    }\r\n  }\r\n\r\n  if (sorting) {\r\n    this._sorting = sorting;\r\n  }\r\n};\r\n\r\n/**\r\n * Insert element at given position\r\n *\r\n * @param {Object} ele\r\n * @param {number} index\r\n * @returns {undefined}\r\n * @access protected\r\n */\r\nPriorityQueue.prototype._insertAt = function(ele, priority, index) {\r\n  if (this._queue.length === index) {\r\n    this._queue.push(ele);\r\n    this._priorities.push(priority);\r\n  } else {\r\n    this._queue.splice(index, 0, ele);\r\n    this._priorities.splice(index, 0, priority);\r\n  }\r\n};\r\n\r\nif (typeof module !== 'undefined' && module.exports) {\r\n  module.exports = PriorityQueue;\r\n}\r\n", "\r\n/**\r\n * @requires ./PriorityQueue.js\r\n */\r\n\r\nif (typeof module !== 'undefined' && module.exports) {\r\n      var PriorityQueue = require('./PriorityQueue.js');\r\n}\r\n\r\n/**\r\n * OPTICS - Ordering points to identify the clustering structure\r\n *\r\n * <AUTHOR> <<EMAIL>>\r\n * @copyright MIT\r\n */\r\n\r\n/**\r\n * OPTICS class constructor\r\n * @constructor\r\n *\r\n * @param {Array} dataset\r\n * @param {number} epsilon\r\n * @param {number} minPts\r\n * @param {function} distanceFunction\r\n * @returns {OPTICS}\r\n */\r\nfunction OPTICS(dataset, epsilon, minPts, distanceFunction) {\r\n  /** @type {number} */\r\n  this.epsilon = 1;\r\n  /** @type {number} */\r\n  this.minPts = 1;\r\n  /** @type {function} */\r\n  this.distance = this._euclideanDistance;\r\n\r\n  // temporary variables used during computation\r\n\r\n  /** @type {Array} */\r\n  this._reachability = [];\r\n  /** @type {Array} */\r\n  this._processed = [];\r\n  /** @type {number} */\r\n  this._coreDistance = 0;\r\n  /** @type {Array} */\r\n  this._orderedList = [];\r\n\r\n  this._init(dataset, epsilon, minPts, distanceFunction);\r\n}\r\n\r\n/******************************************************************************/\r\n// pulic functions\r\n\r\n/**\r\n * Start clustering\r\n *\r\n * @param {Array} dataset\r\n * @returns {undefined}\r\n * @access public\r\n */\r\nOPTICS.prototype.run = function(dataset, epsilon, minPts, distanceFunction) {\r\n  this._init(dataset, epsilon, minPts, distanceFunction);\r\n\r\n  for (var pointId = 0, l = this.dataset.length; pointId < l; pointId++) {\r\n    if (this._processed[pointId] !== 1) {\r\n      this._processed[pointId] = 1;\r\n      this.clusters.push([pointId]);\r\n      var clusterId = this.clusters.length - 1;\r\n\r\n      this._orderedList.push(pointId);\r\n      var priorityQueue = new PriorityQueue(null, null, 'asc');\r\n      var neighbors = this._regionQuery(pointId);\r\n\r\n      // using priority queue assign elements to new cluster\r\n      if (this._distanceToCore(pointId) !== undefined) {\r\n        this._updateQueue(pointId, neighbors, priorityQueue);\r\n        this._expandCluster(clusterId, priorityQueue);\r\n      }\r\n    }\r\n  }\r\n\r\n  return this.clusters;\r\n};\r\n\r\n/**\r\n * Generate reachability plot for all points\r\n *\r\n * @returns {array}\r\n * @access public\r\n */\r\nOPTICS.prototype.getReachabilityPlot = function() {\r\n  var reachabilityPlot = [];\r\n\r\n  for (var i = 0, l = this._orderedList.length; i < l; i++) {\r\n    var pointId = this._orderedList[i];\r\n    var distance = this._reachability[pointId];\r\n\r\n    reachabilityPlot.push([pointId, distance]);\r\n  }\r\n\r\n  return reachabilityPlot;\r\n};\r\n\r\n/******************************************************************************/\r\n// protected functions\r\n\r\n/**\r\n * Set object properties\r\n *\r\n * @param {Array} dataset\r\n * @param {number} epsilon\r\n * @param {number} minPts\r\n * @param {function} distance\r\n * @returns {undefined}\r\n * @access protected\r\n */\r\nOPTICS.prototype._init = function(dataset, epsilon, minPts, distance) {\r\n\r\n  if (dataset) {\r\n\r\n    if (!(dataset instanceof Array)) {\r\n      throw Error('Dataset must be of type array, ' +\r\n        typeof dataset + ' given');\r\n    }\r\n\r\n    this.dataset = dataset;\r\n    this.clusters = [];\r\n    this._reachability = new Array(this.dataset.length);\r\n    this._processed = new Array(this.dataset.length);\r\n    this._coreDistance = 0;\r\n    this._orderedList = [];\r\n  }\r\n\r\n  if (epsilon) {\r\n    this.epsilon = epsilon;\r\n  }\r\n\r\n  if (minPts) {\r\n    this.minPts = minPts;\r\n  }\r\n\r\n  if (distance) {\r\n    this.distance = distance;\r\n  }\r\n};\r\n\r\n/**\r\n * Update information in queue\r\n *\r\n * @param {number} pointId\r\n * @param {Array} neighbors\r\n * @param {PriorityQueue} queue\r\n * @returns {undefined}\r\n * @access protected\r\n */\r\nOPTICS.prototype._updateQueue = function(pointId, neighbors, queue) {\r\n  var self = this;\r\n\r\n  this._coreDistance = this._distanceToCore(pointId);\r\n  neighbors.forEach(function(pointId2) {\r\n    if (self._processed[pointId2] === undefined) {\r\n      var dist = self.distance(self.dataset[pointId], self.dataset[pointId2]);\r\n      var newReachableDistance = Math.max(self._coreDistance, dist);\r\n\r\n      if (self._reachability[pointId2] === undefined) {\r\n        self._reachability[pointId2] = newReachableDistance;\r\n        queue.insert(pointId2, newReachableDistance);\r\n      } else {\r\n        if (newReachableDistance < self._reachability[pointId2]) {\r\n          self._reachability[pointId2] = newReachableDistance;\r\n          queue.remove(pointId2);\r\n          queue.insert(pointId2, newReachableDistance);\r\n        }\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\n/**\r\n * Expand cluster\r\n *\r\n * @param {number} clusterId\r\n * @param {PriorityQueue} queue\r\n * @returns {undefined}\r\n * @access protected\r\n */\r\nOPTICS.prototype._expandCluster = function(clusterId, queue) {\r\n  var queueElements = queue.getElements();\r\n\r\n  for (var p = 0, l = queueElements.length; p < l; p++) {\r\n    var pointId = queueElements[p];\r\n    if (this._processed[pointId] === undefined) {\r\n      var neighbors = this._regionQuery(pointId);\r\n      this._processed[pointId] = 1;\r\n\r\n      this.clusters[clusterId].push(pointId);\r\n      this._orderedList.push(pointId);\r\n\r\n      if (this._distanceToCore(pointId) !== undefined) {\r\n        this._updateQueue(pointId, neighbors, queue);\r\n        this._expandCluster(clusterId, queue);\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Calculating distance to cluster core\r\n *\r\n * @param {number} pointId\r\n * @returns {number}\r\n * @access protected\r\n */\r\nOPTICS.prototype._distanceToCore = function(pointId) {\r\n  var l = this.epsilon;\r\n  for (var coreDistCand = 0; coreDistCand < l; coreDistCand++) {\r\n    var neighbors = this._regionQuery(pointId, coreDistCand);\r\n    if (neighbors.length >= this.minPts) {\r\n      return coreDistCand;\r\n    }\r\n  }\r\n\r\n  return;\r\n};\r\n\r\n/**\r\n * Find all neighbors around given point\r\n *\r\n * @param {number} pointId\r\n * @param {number} epsilon\r\n * @returns {Array}\r\n * @access protected\r\n */\r\nOPTICS.prototype._regionQuery = function(pointId, epsilon) {\r\n  epsilon = epsilon || this.epsilon;\r\n  var neighbors = [];\r\n\r\n  for (var id = 0, l = this.dataset.length; id < l; id++) {\r\n    if (this.distance(this.dataset[pointId], this.dataset[id]) < epsilon) {\r\n      neighbors.push(id);\r\n    }\r\n  }\r\n\r\n  return neighbors;\r\n};\r\n\r\n/******************************************************************************/\r\n// helpers\r\n\r\n/**\r\n * Calculate euclidean distance in multidimensional space\r\n *\r\n * @param {Array} p\r\n * @param {Array} q\r\n * @returns {number}\r\n * @access protected\r\n */\r\nOPTICS.prototype._euclideanDistance = function(p, q) {\r\n  var sum = 0;\r\n  var i = Math.min(p.length, q.length);\r\n\r\n  while (i--) {\r\n    sum += (p[i] - q[i]) * (p[i] - q[i]);\r\n  }\r\n\r\n  return Math.sqrt(sum);\r\n};\r\n\r\nif (typeof module !== 'undefined' && module.exports) {\r\n  module.exports = OPTICS;\r\n}\r\n", "\r\nif (typeof module !== 'undefined' && module.exports) {\r\n    module.exports = {\r\n      DBSCAN: require('./DBSCAN.js'),\r\n      KMEANS: require('./KMEANS.js'),\r\n      OPTICS: require('./OPTICS.js'),\r\n      PriorityQueue: require('./PriorityQueue.js')\r\n    };\r\n}\r\n"], "mappings": ";;;;;AAAA;AAAA;AAiBA,aAAS,OAAO,SAAS,SAAS,QAAQ,kBAAkB;AAE1D,WAAK,UAAU,CAAC;AAEhB,WAAK,UAAU;AAEf,WAAK,SAAS;AAEd,WAAK,WAAW,KAAK;AAErB,WAAK,WAAW,CAAC;AAEjB,WAAK,QAAQ,CAAC;AAKd,WAAK,WAAW,CAAC;AAEjB,WAAK,YAAY,CAAC;AAElB,WAAK,iBAAiB;AAEtB,WAAK,MAAM,SAAS,SAAS,QAAQ,gBAAgB;AAAA,IACvD;AAeA,WAAO,UAAU,MAAM,SAAS,SAAS,SAAS,QAAQ,kBAAkB;AAC1E,WAAK,MAAM,SAAS,SAAS,QAAQ,gBAAgB;AAErD,eAAS,UAAU,GAAG,UAAU,KAAK,gBAAgB,WAAW;AAE9D,YAAI,KAAK,SAAS,OAAO,MAAM,GAAG;AAChC,eAAK,SAAS,OAAO,IAAI;AAGzB,cAAI,YAAY,KAAK,aAAa,OAAO;AAEzC,cAAI,UAAU,SAAS,KAAK,QAAQ;AAClC,iBAAK,MAAM,KAAK,OAAO;AAAA,UACzB,OAAO;AAEL,gBAAI,YAAY,KAAK,SAAS;AAC9B,iBAAK,SAAS,KAAK,CAAC,CAAC;AACrB,iBAAK,cAAc,SAAS,SAAS;AAErC,iBAAK,eAAe,WAAW,SAAS;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AAEA,aAAO,KAAK;AAAA,IACd;AAeA,WAAO,UAAU,QAAQ,SAAS,SAAS,SAAS,QAAQ,UAAU;AAEpE,UAAI,SAAS;AAEX,YAAI,EAAE,mBAAmB,QAAQ;AAC/B,gBAAM,MAAM,oCACV,OAAO,UAAU,QAAQ;AAAA,QAC7B;AAEA,aAAK,UAAU;AACf,aAAK,WAAW,CAAC;AACjB,aAAK,QAAQ,CAAC;AAEd,aAAK,iBAAiB,QAAQ;AAC9B,aAAK,WAAW,IAAI,MAAM,KAAK,cAAc;AAC7C,aAAK,YAAY,IAAI,MAAM,KAAK,cAAc;AAAA,MAChD;AAEA,UAAI,SAAS;AACX,aAAK,UAAU;AAAA,MACjB;AAEA,UAAI,QAAQ;AACV,aAAK,SAAS;AAAA,MAChB;AAEA,UAAI,UAAU;AACZ,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAUA,WAAO,UAAU,iBAAiB,SAAS,WAAW,WAAW;AAM/D,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,YAAI,WAAW,UAAU,CAAC;AAE1B,YAAI,KAAK,SAAS,QAAQ,MAAM,GAAG;AACjC,eAAK,SAAS,QAAQ,IAAI;AAC1B,cAAI,aAAa,KAAK,aAAa,QAAQ;AAE3C,cAAI,WAAW,UAAU,KAAK,QAAQ;AACpC,wBAAY,KAAK,aAAa,WAAW,UAAU;AAAA,UACrD;AAAA,QACF;AAGA,YAAI,KAAK,UAAU,QAAQ,MAAM,GAAG;AAClC,eAAK,cAAc,UAAU,SAAS;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AAQA,WAAO,UAAU,gBAAgB,SAAS,SAAS,WAAW;AAC5D,WAAK,SAAS,SAAS,EAAE,KAAK,OAAO;AACrC,WAAK,UAAU,OAAO,IAAI;AAAA,IAC5B;AAUA,WAAO,UAAU,eAAe,SAAS,SAAS;AAChD,UAAI,YAAY,CAAC;AAEjB,eAAS,KAAK,GAAG,KAAK,KAAK,gBAAgB,MAAM;AAC/C,YAAI,OAAO,KAAK,SAAS,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;AAChE,YAAI,OAAO,KAAK,SAAS;AACvB,oBAAU,KAAK,EAAE;AAAA,QACnB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,WAAO,UAAU,eAAe,SAAS,GAAG,GAAG;AAC7C,UAAI,MAAM,EAAE;AAEZ,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,EAAE,QAAQ,CAAC,IAAI,GAAG;AACpB,YAAE,KAAK,CAAC;AAAA,QACV;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAUA,WAAO,UAAU,qBAAqB,SAAS,GAAG,GAAG;AACnD,UAAI,MAAM;AACV,UAAI,IAAI,KAAK,IAAI,EAAE,QAAQ,EAAE,MAAM;AAEnC,aAAO,KAAK;AACV,gBAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,MACpC;AAEA,aAAO,KAAK,KAAK,GAAG;AAAA,IACtB;AAEA,QAAI,OAAO,WAAW,eAAe,OAAO,SAAS;AACnD,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;AC3OA;AAAA;AAgBC,aAAS,OAAO,SAAS,GAAG,UAAU;AACrC,WAAK,IAAI;AACT,WAAK,UAAU,CAAC;AAChB,WAAK,cAAc,CAAC;AACpB,WAAK,YAAY,CAAC;AAElB,WAAK,KAAK,SAAS,GAAG,QAAQ;AAAA,IAChC;AAKA,WAAO,UAAU,OAAO,SAAS,SAAS,GAAG,UAAU;AACrD,WAAK,cAAc,CAAC;AACpB,WAAK,YAAY,CAAC;AAElB,UAAI,OAAO,YAAY,aAAa;AAClC,aAAK,UAAU;AAAA,MACjB;AAEA,UAAI,OAAO,MAAM,aAAa;AAC5B,aAAK,IAAI;AAAA,MACX;AAEA,UAAI,OAAO,aAAa,aAAa;AACnC,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAKA,WAAO,UAAU,MAAM,SAAS,SAAS,GAAG;AAC1C,WAAK,KAAK,SAAS,CAAC;AAEpB,UAAI,MAAM,KAAK,QAAQ;AAGvB,eAAS,IAAI,GAAG,IAAI,KAAK,GAAG,KAAK;AAC/B,aAAK,UAAU,CAAC,IAAI,KAAK,eAAe;AAAA,MAC3C;AAEC,UAAI,SAAS;AACb,aAAM,QAAQ;AAGZ,iBAAS,KAAK,OAAO;AAGrB,iBAAS,aAAa,GAAG,aAAa,KAAK,GAAG,cAAc;AAC1D,cAAI,OAAO,IAAI,MAAM,MAAM;AAC3B,cAAI,QAAQ;AAGZ,mBAAS,MAAM,GAAG,MAAM,QAAQ,OAAO;AACrC,iBAAK,GAAG,IAAI;AAAA,UACd;AAEA,mBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,gBAAI,SAAS,KAAK,QAAQ,CAAC,EAAE;AAG7B,gBAAI,eAAe,KAAK,YAAY,CAAC,GAAG;AACtC,uBAAS,MAAM,GAAG,MAAM,QAAQ,OAAO;AACrC,qBAAK,GAAG,KAAK,KAAK,QAAQ,CAAC,EAAE,GAAG;AAAA,cAClC;AACA;AAAA,YACF;AAAA,UACF;AAEA,cAAI,QAAQ,GAAG;AAEb,qBAAS,MAAM,GAAG,MAAM,QAAQ,OAAO;AACrC,mBAAK,GAAG,KAAK;AAAA,YACf;AACA,iBAAK,UAAU,UAAU,IAAI;AAAA,UAC/B,OAAO;AAEL,iBAAK,UAAU,UAAU,IAAI,KAAK,eAAe;AACjD,qBAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF;AAEA,aAAO,KAAK,YAAY;AAAA,IAC1B;AAOA,WAAO,UAAU,iBAAiB,WAAW;AAC3C,UAAI,QAAQ,KAAK,QAAQ,SAAQ;AACjC,UAAI;AACJ,UAAI;AAEJ,SAAG;AACD,aAAK,KAAK,MAAM,KAAK,OAAO,IAAI,KAAK;AACrC,mBAAW,KAAK,QAAQ,EAAE;AAAA,MAC5B,SAAS,KAAK,UAAU,QAAQ,QAAQ,KAAK;AAE7C,aAAO;AAAA,IACT;AAOA,WAAO,UAAU,SAAS,WAAW;AACnC,UAAI,SAAS;AACb,UAAI,MAAM,KAAK,QAAQ;AACvB,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,0BAAkB,KAAK,OAAO,KAAK,QAAQ,CAAC,GAAG,KAAK,WAAW,KAAK,QAAQ;AAE5E,YAAI,mBAAmB,KAAK,YAAY,CAAC,GAAG;AAC1C,eAAK,YAAY,CAAC,IAAI;AACtB,mBAAS;AAAA,QACX;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAOA,WAAO,UAAU,cAAc,WAAW;AACxC,UAAI,WAAW,IAAI,MAAM,KAAK,CAAC;AAC/B,UAAI;AAEJ,eAAS,UAAU,GAAG,UAAU,KAAK,YAAY,QAAQ,WAAW;AAClE,qBAAa,KAAK,YAAY,OAAO;AAGrC,YAAI,OAAO,SAAS,UAAU,MAAM,aAAa;AAC/C,mBAAS,UAAU,IAAI,CAAC;AAAA,QAC1B;AAEA,iBAAS,UAAU,EAAE,KAAK,OAAO;AAAA,MACnC;AAEA,aAAO;AAAA,IACT;AAUA,WAAO,UAAU,SAAS,SAAS,OAAO,KAAK,GAAG;AAChD,UAAI,MAAM,OAAO;AACjB,UAAI,MAAM;AACV,UAAI,MAAM,IAAI;AACd,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAI,EAAE,OAAO,IAAI,CAAC,CAAC;AACnB,YAAI,IAAI,KAAK;AACX,gBAAM;AACN,gBAAM;AAAA,QACR;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AASA,WAAO,UAAU,WAAW,SAAS,GAAG,GAAG;AACzC,UAAI,MAAM;AACV,UAAI,IAAI,KAAK,IAAI,EAAE,QAAQ,EAAE,MAAM;AAEnC,aAAO,KAAK;AACV,YAAI,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC;AACrB,eAAO,OAAO;AAAA,MAChB;AAEA,aAAO,KAAK,KAAK,GAAG;AAAA,IACtB;AAEA,QAAI,OAAO,WAAW,eAAe,OAAO,SAAS;AACnD,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACpNA;AAAA;AAsBA,aAAS,cAAc,UAAU,YAAY,SAAS;AAEpD,WAAK,SAAS,CAAC;AAEf,WAAK,cAAc,CAAC;AAEpB,WAAK,WAAW;AAEhB,WAAK,MAAM,UAAU,YAAY,OAAO;AAAA,IAC1C;AAUA,kBAAc,UAAU,SAAS,SAAS,KAAK,UAAU;AACvD,UAAI,gBAAgB,KAAK,OAAO;AAChC,UAAI,QAAQ;AAEZ,aAAO,SAAS;AACd,YAAI,YAAY,KAAK,YAAY,KAAK;AACtC,YAAI,KAAK,aAAa,QAAQ;AAC5B,cAAI,WAAW,WAAW;AACxB,4BAAgB;AAAA,UAClB;AAAA,QACF,OAAO;AACL,cAAI,WAAW,WAAW;AACxB,4BAAgB;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAEA,WAAK,UAAU,KAAK,UAAU,aAAa;AAAA,IAC7C;AASA,kBAAc,UAAU,SAAS,SAAS,KAAK;AAC7C,UAAI,QAAQ,KAAK,OAAO;AAExB,aAAO,SAAS;AACd,YAAI,OAAO,KAAK,OAAO,KAAK;AAC5B,YAAI,QAAQ,MAAM;AAChB,eAAK,OAAO,OAAO,OAAO,CAAC;AAC3B,eAAK,YAAY,OAAO,OAAO,CAAC;AAChC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AASA,kBAAc,UAAU,UAAU,SAAS,MAAM;AAC/C,WAAK,OAAO,QAAQ,IAAI;AAAA,IAC1B;AAMA,kBAAc,UAAU,cAAc,WAAW;AAC/C,aAAO,KAAK;AAAA,IACd;AAOA,kBAAc,UAAU,qBAAqB,SAAS,OAAO;AAC3D,aAAO,KAAK,YAAY,KAAK;AAAA,IAC/B;AAMA,kBAAc,UAAU,gBAAgB,WAAW;AACjD,aAAO,KAAK;AAAA,IACd;AAMA,kBAAc,UAAU,4BAA4B,WAAW;AAC7D,UAAI,SAAS,CAAC;AAEd,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,IAAI,GAAG,KAAK;AAClD,eAAO,KAAK,CAAC,KAAK,OAAO,CAAC,GAAG,KAAK,YAAY,CAAC,CAAC,CAAC;AAAA,MACnD;AAEA,aAAO;AAAA,IACT;AAUA,kBAAc,UAAU,QAAQ,SAAS,UAAU,YAAY,SAAS;AAEtE,UAAI,YAAY,YAAY;AAC1B,aAAK,SAAS,CAAC;AACf,aAAK,cAAc,CAAC;AAEpB,YAAI,SAAS,WAAW,WAAW,QAAQ;AACzC,gBAAM,IAAI,MAAM,kCAAkC;AAAA,QACpD;AAEA,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,eAAK,OAAO,SAAS,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,QACxC;AAAA,MACF;AAEA,UAAI,SAAS;AACX,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAUA,kBAAc,UAAU,YAAY,SAAS,KAAK,UAAU,OAAO;AACjE,UAAI,KAAK,OAAO,WAAW,OAAO;AAChC,aAAK,OAAO,KAAK,GAAG;AACpB,aAAK,YAAY,KAAK,QAAQ;AAAA,MAChC,OAAO;AACL,aAAK,OAAO,OAAO,OAAO,GAAG,GAAG;AAChC,aAAK,YAAY,OAAO,OAAO,GAAG,QAAQ;AAAA,MAC5C;AAAA,IACF;AAEA,QAAI,OAAO,WAAW,eAAe,OAAO,SAAS;AACnD,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACnLA;AAAA;AAKA,QAAI,OAAO,WAAW,eAAe,OAAO,SAAS;AAC3C,sBAAgB;AAAA,IAC1B;AADU;AAoBV,aAAS,OAAO,SAAS,SAAS,QAAQ,kBAAkB;AAE1D,WAAK,UAAU;AAEf,WAAK,SAAS;AAEd,WAAK,WAAW,KAAK;AAKrB,WAAK,gBAAgB,CAAC;AAEtB,WAAK,aAAa,CAAC;AAEnB,WAAK,gBAAgB;AAErB,WAAK,eAAe,CAAC;AAErB,WAAK,MAAM,SAAS,SAAS,QAAQ,gBAAgB;AAAA,IACvD;AAYA,WAAO,UAAU,MAAM,SAAS,SAAS,SAAS,QAAQ,kBAAkB;AAC1E,WAAK,MAAM,SAAS,SAAS,QAAQ,gBAAgB;AAErD,eAAS,UAAU,GAAG,IAAI,KAAK,QAAQ,QAAQ,UAAU,GAAG,WAAW;AACrE,YAAI,KAAK,WAAW,OAAO,MAAM,GAAG;AAClC,eAAK,WAAW,OAAO,IAAI;AAC3B,eAAK,SAAS,KAAK,CAAC,OAAO,CAAC;AAC5B,cAAI,YAAY,KAAK,SAAS,SAAS;AAEvC,eAAK,aAAa,KAAK,OAAO;AAC9B,cAAI,gBAAgB,IAAI,cAAc,MAAM,MAAM,KAAK;AACvD,cAAI,YAAY,KAAK,aAAa,OAAO;AAGzC,cAAI,KAAK,gBAAgB,OAAO,MAAM,QAAW;AAC/C,iBAAK,aAAa,SAAS,WAAW,aAAa;AACnD,iBAAK,eAAe,WAAW,aAAa;AAAA,UAC9C;AAAA,QACF;AAAA,MACF;AAEA,aAAO,KAAK;AAAA,IACd;AAQA,WAAO,UAAU,sBAAsB,WAAW;AAChD,UAAI,mBAAmB,CAAC;AAExB,eAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,IAAI,GAAG,KAAK;AACxD,YAAI,UAAU,KAAK,aAAa,CAAC;AACjC,YAAI,WAAW,KAAK,cAAc,OAAO;AAEzC,yBAAiB,KAAK,CAAC,SAAS,QAAQ,CAAC;AAAA,MAC3C;AAEA,aAAO;AAAA,IACT;AAeA,WAAO,UAAU,QAAQ,SAAS,SAAS,SAAS,QAAQ,UAAU;AAEpE,UAAI,SAAS;AAEX,YAAI,EAAE,mBAAmB,QAAQ;AAC/B,gBAAM,MAAM,oCACV,OAAO,UAAU,QAAQ;AAAA,QAC7B;AAEA,aAAK,UAAU;AACf,aAAK,WAAW,CAAC;AACjB,aAAK,gBAAgB,IAAI,MAAM,KAAK,QAAQ,MAAM;AAClD,aAAK,aAAa,IAAI,MAAM,KAAK,QAAQ,MAAM;AAC/C,aAAK,gBAAgB;AACrB,aAAK,eAAe,CAAC;AAAA,MACvB;AAEA,UAAI,SAAS;AACX,aAAK,UAAU;AAAA,MACjB;AAEA,UAAI,QAAQ;AACV,aAAK,SAAS;AAAA,MAChB;AAEA,UAAI,UAAU;AACZ,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAWA,WAAO,UAAU,eAAe,SAAS,SAAS,WAAW,OAAO;AAClE,UAAI,OAAO;AAEX,WAAK,gBAAgB,KAAK,gBAAgB,OAAO;AACjD,gBAAU,QAAQ,SAAS,UAAU;AACnC,YAAI,KAAK,WAAW,QAAQ,MAAM,QAAW;AAC3C,cAAI,OAAO,KAAK,SAAS,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,QAAQ,CAAC;AACtE,cAAI,uBAAuB,KAAK,IAAI,KAAK,eAAe,IAAI;AAE5D,cAAI,KAAK,cAAc,QAAQ,MAAM,QAAW;AAC9C,iBAAK,cAAc,QAAQ,IAAI;AAC/B,kBAAM,OAAO,UAAU,oBAAoB;AAAA,UAC7C,OAAO;AACL,gBAAI,uBAAuB,KAAK,cAAc,QAAQ,GAAG;AACvD,mBAAK,cAAc,QAAQ,IAAI;AAC/B,oBAAM,OAAO,QAAQ;AACrB,oBAAM,OAAO,UAAU,oBAAoB;AAAA,YAC7C;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAUA,WAAO,UAAU,iBAAiB,SAAS,WAAW,OAAO;AAC3D,UAAI,gBAAgB,MAAM,YAAY;AAEtC,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,IAAI,GAAG,KAAK;AACpD,YAAI,UAAU,cAAc,CAAC;AAC7B,YAAI,KAAK,WAAW,OAAO,MAAM,QAAW;AAC1C,cAAI,YAAY,KAAK,aAAa,OAAO;AACzC,eAAK,WAAW,OAAO,IAAI;AAE3B,eAAK,SAAS,SAAS,EAAE,KAAK,OAAO;AACrC,eAAK,aAAa,KAAK,OAAO;AAE9B,cAAI,KAAK,gBAAgB,OAAO,MAAM,QAAW;AAC/C,iBAAK,aAAa,SAAS,WAAW,KAAK;AAC3C,iBAAK,eAAe,WAAW,KAAK;AAAA,UACtC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AASA,WAAO,UAAU,kBAAkB,SAAS,SAAS;AACnD,UAAI,IAAI,KAAK;AACb,eAAS,eAAe,GAAG,eAAe,GAAG,gBAAgB;AAC3D,YAAI,YAAY,KAAK,aAAa,SAAS,YAAY;AACvD,YAAI,UAAU,UAAU,KAAK,QAAQ;AACnC,iBAAO;AAAA,QACT;AAAA,MACF;AAEA;AAAA,IACF;AAUA,WAAO,UAAU,eAAe,SAAS,SAAS,SAAS;AACzD,gBAAU,WAAW,KAAK;AAC1B,UAAI,YAAY,CAAC;AAEjB,eAAS,KAAK,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK,GAAG,MAAM;AACtD,YAAI,KAAK,SAAS,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC,IAAI,SAAS;AACpE,oBAAU,KAAK,EAAE;AAAA,QACnB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAaA,WAAO,UAAU,qBAAqB,SAAS,GAAG,GAAG;AACnD,UAAI,MAAM;AACV,UAAI,IAAI,KAAK,IAAI,EAAE,QAAQ,EAAE,MAAM;AAEnC,aAAO,KAAK;AACV,gBAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,MACpC;AAEA,aAAO,KAAK,KAAK,GAAG;AAAA,IACtB;AAEA,QAAI,OAAO,WAAW,eAAe,OAAO,SAAS;AACnD,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;AC5QA;AAAA;AACA,QAAI,OAAO,WAAW,eAAe,OAAO,SAAS;AACjD,aAAO,UAAU;AAAA,QACf,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,eAAe;AAAA,MACjB;AAAA,IACJ;AAAA;AAAA;", "names": []}