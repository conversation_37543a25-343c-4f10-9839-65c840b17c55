import React, { useState, useEffect } from "react";
import LogisticRegressionForm from "../../MLModelForm/LogisticRegressionForm";
import SVCForm from "../../MLModelForm/SVCForm";
import NaiveBayesForm from "../../MLModelForm/NaiveBayesForm";
import DecisionTreeForm from "../../MLModelForm/DecisionTreeForm";
import RandomForestForm from "../../MLModelForm/RandomForestForm";
import KNearestNeighborsForm from "../../MLModelForm/KNearestNeighborsForm";
import GradientBoostingForm from "../../MLModelForm/GradientBoostingForm";
import AdaBoostForm from "../../MLModelForm/AdaBoostForm";
import LinearRegressionForm from "../../MLModelForm/LinearRegressionForm";
import MLPForm from "../../MLModelForm/MLPForm";
import RidgeRegressionForm from "../../MLModelForm/RidgeRegressionForm";
import LassoRegressionForm from "../../MLModelForm/LassoRegressionForm";
import DecisionTreeRegressorForm from "../../MLModelForm/DecisionTreeRegressorForm";
import RandomForestRegressorForm from "../../MLModelForm/RandomForestRegressorForm";
import KNearestNeighborsRegressorForm from "../../MLModelForm/KNearestNeighborsRegressorForm";

import { useSelector } from "react-redux";

/**
 * ModelPredictionForm component for configuring and submitting machine learning model predictions.
 *
 * This form allows users to:
 * - Select a target column (label) from available dataset features.
 * - Choose a machine learning model (classification or regression).
 * - Configure model-specific hyperparameters via dynamic sub-forms.
 * - Select whether to use a cross-validation set and specify the train/validation/test split ratio.
 * - (Optionally) Select feature values for prediction.
 * - Submit the configuration to trigger model training and prediction.
 *
 * @component
 * @param {Object} props - Component props
 * @param {function(Object):void} props.onPredict - Callback invoked with the prediction request object when the form is submitted.
 * @param {string} props.targetColumn - The currently selected target column (label).
 * @param {function(string):void} props.setTargetColumn - Setter for updating the selected target column.
 *
 * @example
 * <ModelPredictionForm
 *   onPredict={handlePredict}
 *   targetColumn={targetColumn}
 *   setTargetColumn={setTargetColumn}
 * />
 *
 * Redux State Dependencies:
 * - state.upload.features: Contains feature metadata, including unassigned, numerical, nominal, and ordinal features.
 *   - features.ordinal.items: Used to build ordinal value mapping.
 *   - features.allFeatureOrder: Used to preserve feature order.
 *   - features.featureValuesMap: (Optional) Used for feature value selection.
 */

const ModelPredictionForm = ({
  onPredict, // Callback to handle prediction submission
  targetColumn, // Currently selected target column (label)
  setTargetColumn, // Setter for updating the target column
}) => {
  const features = useSelector((state) => state.upload.features);

  const ordinalItems = features.ordinal.items || [];
  const ordinalMap = {};
  ordinalItems.forEach((item) => {
    ordinalMap[item.feature] = item.uniqueValue || [];
  });

  // Create a map of all items by id for fast lookup
  const allItemsMap = {};
  [
    ...features.unassigned.items,
    ...features.numerical.items,
    ...features.nominal.items,
    ...features.ordinal.items,
  ].forEach((item) => {
    allItemsMap[item.id] = item;
  });

  // Use the original order
  // ถ้ามี features.allFeatureOrder ใช้เลย ไม่งั้น fallback เป็น key list
  const allFeatureOrder = features.allFeatureOrder || Object.keys(allItemsMap);

  /**
  * Map of feature names to their possible values.
  * Typically used for categorical/ordinal features.
  */
  const featureValuesMap = features.featureValuesMap || {};

  // แปลง allFeatureOrder เป็น array ของชื่อฟีเจอร์
  const availableColumns = allFeatureOrder
    .map((id) => allItemsMap[id]?.feature)
    .filter(Boolean);

  // Selected model type, e.g., "logistic_regression", "svc", etc.
  const [selectedModel, setSelectedModel] = useState("");

  /**
  * Stores hyperparameter values for the selected model.
  * Passed to the backend during training.
  */
  const [modelParams, setModelParams] = useState({});

  /**
  * Flag indicating whether cross-validation should be used.
  */
  const [useCrossValidation, setUseCrossValidation] = useState(false);

  /**
  * String representing dataset split ratio (e.g., "80:20" or "70:10:20")
  */
  const [splitRatio, setSplitRatio] = useState("80:20");

  // ค่าฟีเจอร์ที่เลือกไว้(ใช้สำหรับ prediction ภายหลัง)
  const [selectedFeatureValues, setSelectedFeatureValues] = useState({});

  /**
  * Updates selected model and resets its parameters.
  *
  * @param {React.ChangeEvent<HTMLSelectElement>} e
  */
  const handleModelChange = (e) => {
    setSelectedModel(e.target.value);
    setModelParams({});
  };

  /**
  * Updates the state when the user changes model parameters via subform.
  *
  * @param {Object} params - New model parameters
  */
  const handleParamsChange = (params) => {
    setModelParams(params);
  };

  /**
  * Gathers the full model configuration and submits it to the parent handler.
  *
  * @param {React.FormEvent<HTMLFormElement>} e
  */
  const handleSubmit = (e) => {
    e.preventDefault();
    const predictionRequest = {
      model: selectedModel,
      params: modelParams,
      useCrossValidation,
      splitRatio,
      targetColumn,
      selectedFeatureValues,
    };
    onPredict(predictionRequest);
  };

  useEffect(() => {
    if (useCrossValidation) {
      setSplitRatio("70:10:20");
    } else {
      setSplitRatio("80:20");
    }
  }, [useCrossValidation]);

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Use Cross Validation */}
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          checked={useCrossValidation}
          onChange={(e) => setUseCrossValidation(e.target.checked)}
        />
        <label className="text-sm">Use Cross Validation Set</label>
      </div>

      {/* Split Ratio Dropdown */}
      <div>
        <label className="block text-sm font-medium">Split Dataset:</label>
        <select
          value={splitRatio}
          onChange={(e) => setSplitRatio(e.target.value)}
          className="mt-1 p-2 border rounded w-full"
        >
          {!useCrossValidation && (
            <>
              <option value="50:50">50:50</option>
              <option value="60:40">60:40</option>
              <option value="70:30">70:30</option>
              <option value="80:20">80:20</option>
              <option value="90:10">90:10</option>
            </>
          )}
          {useCrossValidation && (
            <>
              <option value="50:10:40">50:10:40</option>
              <option value="60:20:20">60:20:20</option>
              <option value="70:10:20">70:10:20</option>
              <option value="80:10:10">80:10:10</option>
              <option value="90:5:5">90:5:5</option>
            </>
          )}
        </select>
      </div>

      {/* Label (Target Column) */}
      <div>
        <label className="block text-sm font-medium">Label (Target Column):</label>
        <select
          value={targetColumn}
          onChange={(e) => setTargetColumn(e.target.value)}
          className="mt-1 p-2 border rounded w-full"
        >
          <option value="">-- Select Target Column --</option>
          {availableColumns.map((col) => (
            <option key={col} value={col}>
              {col}
            </option>
          ))}
        </select>
      </div>

      {/* Model Selection */}
      <div>
        <label className="block text-sm font-medium">Select Model:</label>
        <select
          value={selectedModel}
          onChange={handleModelChange}
          className="mt-1 p-2 border rounded w-full"
        >
          <option value="">-- Select --</option>
          <option value="logistic_regression">Logistic Regression</option>
          <option value="svc">Support Vector Classifier</option>
          <option value="naive_bayes">Multinomial Naive Bayes</option>
          <option value="decision_tree">Decision Tree</option>
          <option value="random_forest">Random Forest</option>
          <option value="knn">K-Nearest Neighbors</option>
          <option value="gradient_boosting">Gradient Boosting Classifier</option>
          <option value="ada_boost">AdaBoost Classifier</option>
          <option value="mlp">Multi-Layer Perceptron Classifier</option>
          <option value="linear_regression">Linear Regression</option>
          <option value="ridge_regression">Ridge Regression</option>
          <option value="lasso_regression">Lasso Regression</option>
          <option value="decision_tree_regressor">Decision Tree Regressor</option>
          <option value="random_forest_regressor">Random Forest Regressor</option>
          <option value="knn_regressor">K-Nearest Neighbors Regressor</option>
        </select>
      </div>

      {/* Dynamic Model Form */}
      {selectedModel === "logistic_regression" && (
        <LogisticRegressionForm onParamsChange={handleParamsChange} />
      )}
      {selectedModel === "svc" && <SVCForm onParamsChange={handleParamsChange} />}
      {selectedModel === "naive_bayes" && <NaiveBayesForm onParamsChange={handleParamsChange} />}
      {selectedModel === "decision_tree" && <DecisionTreeForm onParamsChange={handleParamsChange} />}
      {selectedModel === "random_forest" && <RandomForestForm onParamsChange={handleParamsChange} />}
      {selectedModel === "knn" && <KNearestNeighborsForm onParamsChange={handleParamsChange} />}
      {selectedModel === "gradient_boosting" && (
        <GradientBoostingForm onParamsChange={handleParamsChange} />
      )}
      {selectedModel === "ada_boost" && <AdaBoostForm onParamsChange={handleParamsChange} />}
      {selectedModel === "mlp" && <MLPForm onParamsChange={handleParamsChange} />}
      {selectedModel === "linear_regression" && <LinearRegressionForm onParamsChange={handleParamsChange} />}
      {selectedModel === "ridge_regression" && <RidgeRegressionForm onParamsChange={handleParamsChange} />}
      {selectedModel === "lasso_regression" && <LassoRegressionForm onParamsChange={handleParamsChange} />}
      {selectedModel === "decision_tree_regressor" && (
        <DecisionTreeRegressorForm onParamsChange={handleParamsChange} />
      )}
      {selectedModel === "random_forest_regressor" && (
        <RandomForestRegressorForm onParamsChange={handleParamsChange} />
      )}
      {selectedModel === "knn_regressor" && <KNearestNeighborsRegressorForm onParamsChange={handleParamsChange} />}

      {/* Submit Button */}
      <button
        type="submit"
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
      >
        Train
      </button>
    </form>
  );
};

export default ModelPredictionForm;