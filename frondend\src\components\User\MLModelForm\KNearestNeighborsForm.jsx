import React, { useState, useEffect } from "react";

const KNearestNeighborsForm = ({ onParamsChange }) => {
    const [params, setParams] = useState({
        n_neighbors: 5,
        weights: "uniform",
        metric: "minkowski",
    });

    useEffect(() => {
        onParamsChange(params);
    }, [params, onParamsChange]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setParams((prevParams) => ({
            ...prevParams,
            [name]: value,
        }));
    };

    return (
        <div className="space-y-2 p-4 bg-white rounded shadow">
            <div>
                <label className="block text-sm font-medium">n_neighbors:</label>
                <input
                    type="number"
                    name="n_neighbors"
                    value={params.n_neighbors}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="1"
                />
            </div>


            <div>
                <label className="block text-sm font-medium">Weights:</label>
                <select
                    name="weights"
                    value={params.weights}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="uniform">Uniform</option>
                    <option value="distance">Distance</option>
                </select>
            </div>

            <div>
                <label className="block text-sm font-medium">Metric:</label>
                <select
                    name="metric"
                    value={params.metric}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="euclidean">Euclidean</option>
                    <option value="manhattan">Manhattan</option>
                    <option value="minkowski">Minkowski</option>
                    <option value="cosine">Cosine </option>
                </select>
            </div>
        </div>
    );
};

export default KNearestNeighborsForm;
