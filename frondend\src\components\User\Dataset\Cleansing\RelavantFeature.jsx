import React from "react";
import { useSelector, useDispatch } from "react-redux";
import { toggleItem } from "../../../../app/features/progress/upload/uploadSlice"; // action creator ที่ใช้สลับค่า disabled

/**
 * Utility function to join class names conditionally.
 * @param {...string} classes - List of class names.
 * @returns {string} Concatenated class names.
 */
const classNames = (...classes) => classes.filter(Boolean).join(" ");

/**
 * RelevantFeature component for selecting relevant dataset features.
 *
 * Displays all features (unassigned, numerical, nominal, ordinal) as a list of checkboxes.
 * Allows users to toggle the "relevant" status of each feature.
 * Features that are disabled are visually indicated and cannot be toggled.
 * Uses Redux for state management.
 *
 * @component
 * @returns {JSX.Element}
 *
 * @example
 * <RelevantFeature />
 */
export default function RelevantFeature() {
  const dispatch = useDispatch();
  /**
  * Features object from Redux state.
  * @type {import('../../../../app/features/progress/upload/uploadSlice').FeaturesState}
  */
  const features = useSelector((state) => state.upload.features); // object ที่ประกอบด้วย unassigned, numerical, nominal, ordinal (แต่ละอันมี .items array)

  /**
  * Map of all feature items by their id for quick lookup.
  * @type {Object.<string, {id: string, feature: string, disabled: boolean}>}
  */
  // Create a map of all items by id for fast lookup
  const allItemsMap = {};
  [
    ...features.unassigned.items,
    ...features.numerical.items,
    ...features.nominal.items,
    ...features.ordinal.items,
  ].forEach((item) => {
    allItemsMap[item.id] = item;
  });

  /**
  * Ordered array of all feature ids.
  * @type {string[]}
  */
  // Use the original order
  const allFeatureOrder = features.allFeatureOrder || Object.keys(allItemsMap);

  /**
  * Handles toggling the relevant/disabled state of a feature.
  * Dispatches the toggleItem action to Redux.
  * @param {string} id - The id of the feature to toggle.
  */
  const handleToggleItem = (id) => {
    dispatch(toggleItem(id));
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <p className="text-lg mb-6">Select only relevant feature</p>
      <div className="bg-gray-300 bg-opacity-30 rounded-xl p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* ใช้ allFeatureOrder เพื่อให้ลำดับการแสดงผลคงที่ */}
          {/* ดึง item จาก allItemsMap */}
          {/* ถ้าไม่เจอ (null/undefined) ให้ข้าม */}
          {allFeatureOrder.map((id) => {
            const item = allItemsMap[id];
            if (!item) return null;
            return (
              <div key={item.id} className="flex items-center">
                <label
                  htmlFor={item.id}
                  className="flex items-center w-full bg-white rounded-full px-4 py-2 cursor-pointer"
                >
                  <div
                    className={classNames(
                      "w-8 h-8 rounded-full flex items-center justify-center mr-3",
                      item.disabled ? "bg-gray-400" : "bg-green-500"
                    )}
                  >
                    {item.disabled && (
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="text-white"
                      >
                        <path
                          d="M20 6L9 17L4 12"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    )}
                  </div>
                  <span
                    className={classNames(
                      "text-lg",
                      item.disabled
                        ? "text-gray-400 line-through"
                        : "text-black"
                    )}
                  >
                    {item.feature}
                  </span>
                  {/* ใช้ checked ตามค่าจาก Redux */}
                  <input
                    type="checkbox"
                    id={item.id}
                    checked={item.disabled}
                    onChange={() => handleToggleItem(item.id)}
                    className="sr-only"
                  />
                </label>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
