import React, { useState, useEffect } from "react";

const DecisionTreeForm = ({ onParamsChange }) => {
    const [params, setParams] = useState({
        criterion: "gini",
        max_depth: 1,
        min_samples_split: 2,
    });

    useEffect(() => {
        onParamsChange(params);
    }, [params, onParamsChange]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setParams((prevParams) => ({
            ...prevParams,
            [name]: value,
        }));
    };

    return (
        <div className="space-y-2 p-4 bg-white rounded shadow">
            <div>
                <label className="block text-sm font-medium">Criterion:</label>
                <select
                    name="criterion"
                    value={params.criterion}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    {/* Classification Options */}
                    <option value="gini">Gini Impurity (Classification)</option>
                    <option value="entropy">Entropy (Classification)</option>

                    {/* Regression Options */}
                    <option value="squared_error">Mean Squared Error (Regression)</option>
                    <option value="friedman_mse">Friedman MSE (Regression)</option>
                    <option value="absolute_error">Mean Absolute Error (Regression)</option>
                </select>
            </div>


            <div>
                <label className="block text-sm font-medium">max_depth:</label>
                <input
                    type="number"
                    name="max_depth"
                    value={params.max_depth}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="1"
                />
            </div>

            <div>
                <label className="block text-sm font-medium">min_samples_split:</label>
                <input
                    type="number"
                    name="min_samples_split"
                    value={params.min_samples_split}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="1"
                />
            </div>
        </div>
    );
};

export default DecisionTreeForm;
