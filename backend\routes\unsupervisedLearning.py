from flask import Blueprint, request, jsonify
import pandas as pd
from sklearn.cluster import KMeans, DBSCAN
from sklearn.metrics import silhouette_score, davies_bouldin_score, calinski_harabasz_score
from sklearn.decomposition import PCA
from db import init_db
from bson import ObjectId

unsupervised_bp = Blueprint('unsupervised', __name__)

# MongoDB
from flask import Flask
app = Flask(__name__)
mongo = init_db(app)

@unsupervised_bp.route('/clustering', methods=['POST'])
def clustering():
    try:
        # รับข้อมูลจาก request
        data = request.get_json()

        # รับข้อมูลจาก user
        dataset_id = data.get("dataset_id") # ID ของ dataset
        model_type = data.get("model_type")  # ประเภทของโมเดลที่ต้องการใช้ KMeans, AgglomerativeClustering, DBSCAN
        params = data.get("params")  # พารามิเตอร์ของโมเดล

        # ตรวจสอบว่ามีการระบุ dataset_id และ model_type หรือไม่
        if not dataset_id or not model_type:
            return jsonify({"error": "dataset_id and model_type are required"}), 400

        # โหลด dataset จาก current หรือ previous
        current_doc = mongo.db.current.find_one({"dataset_id": ObjectId(dataset_id)})
        if current_doc:
            df = pd.DataFrame(current_doc["data"])
        else:
            previous_doc = mongo.db.previous.find_one({"dataset_id": ObjectId(dataset_id)})
            if not previous_doc:
                return jsonify({"error": "Dataset not found"}), 404
            df = pd.DataFrame(previous_doc["data"])

        # ตรวจสอบว่า dataframe มีข้อมูลประเภทตัวเลขหรือไม่
        if df.empty or df.select_dtypes(include=["number"]).empty:
            return jsonify({"error": "No numeric data available for clustering"}), 400
        
        # เลือกเฉพาะคอลัมน์ที่เป็นตัวเลขและลบค่า NaN
        df = df.select_dtypes(include=["number"]).dropna()

        # แปลงค่าใน DataFrame ให้เป็นชนิดข้อมูลที่รองรับการแปลงเป็น JSON
        df = df.apply(lambda x: x.astype('float64') if x.dtype.kind in 'if' else x)

        # เลือกโมเดลที่ต้องการ
        if model_type == "kmeans":
            model = KMeans(
                n_clusters=params.get("n_clusters", 3), # จำนวนของคลัสเตอร์ที่ต้องการ
                init=params.get("init", "k-means++"), # k-means++, random
                max_iter=params.get("max_iter", 300) # จำนวนการทำซ้ำ
            )

        elif model_type == "dbscan":
            model = DBSCAN(
                eps=params.get("eps", 0.5), # ความคล้ายคลึงที่ใช้ในการจัดกลุ่ม
                min_samples=params.get("min_samples", 5), # จำนวนข้อมูลขั้นต่ำในกลุ่ม
                metric=params.get("metric", "euclidean") # วิธีการคำนวณระยะห่าง
            )
        else:
            return jsonify({"error": "Invalid model type"}), 400

        # ฝึกโมเดล
        model.fit(df)

        # คำนวณ metrics (แยกตามโมเดล)
        result = {}

        # สำหรับ KMeans
        if model_type == "kmeans":
            inertia = model.inertia_  # KMeans ใช้ inertia_
            result["inertia"] = inertia
            
            # Silhouette Score
            silhouette = silhouette_score(df, model.labels_)
            result["silhouette_score"] = silhouette

            # Calinski-Harabasz Index
            calinski_harabasz = calinski_harabasz_score(df, model.labels_)
            result["calinski_harabasz_index"] = calinski_harabasz

            # Davies-Bouldin Index
            db_index = davies_bouldin_score(df, model.labels_)
            result["davies_bouldin_index"] = db_index

        # สำหรับ DBSCAN
        else:
            if hasattr(model, 'labels_') and model.labels_ is not None:
                # Silhouette Score
                silhouette = silhouette_score(df, model.labels_)
                result["silhouette_score"] = silhouette

                # Calinski-Harabasz Index
                calinski_harabasz = calinski_harabasz_score(df, model.labels_)
                result["calinski_harabasz_index"] = calinski_harabasz

                # Davies-Bouldin Index
                db_index = davies_bouldin_score(df, model.labels_)
                result["davies_bouldin_index"] = db_index
            else:
                result["error"] = "Model labels not available for evaluation."

        # แสดงผลข้อมูลของกลุ่ม
        result["data_points"] = [{"id": idx, "x": float(point[0]), "y": float(point[1]), "cluster": int(label)} 
            for idx, (point, label) in enumerate(zip(df.values, model.labels_))
        ]

        # แสดงผลของคลัสเตอร์
        result["cluster_centers"] = [{"x": float(center[0]), "y": float(center[1]), "cluster": int(idx)} 
            for idx, center in enumerate(model.cluster_centers_)
        ] if model_type == "kmeans" else []

        # ข้อมูล Meta
        result["meta"] = {
            "num_clusters": len(set(model.labels_)),
            "reduction_method": "PCA", # การลดมิติ (Dimensionality reduction) เช่น PCA
            "explained_variance": list(PCA().fit(df).explained_variance_ratio_) # คำนวณอธิบายความแปรปรวน
        }

        result["hyperparameters"] = params
        
        # บันทึกลง datasets_cluster
        mongo.db.datasets_cluster.update_one(
            {"dataset_id": ObjectId(dataset_id)},
            {"$set": {
                "inertia": result.get("inertia"),
                "silhouette_score": result.get("silhouette_score"),
                "calinski_harabasz_index": result.get("calinski_harabasz_index"),
                "davies_bouldin_index": result.get("davies_bouldin_index"),
                "data_points": result["data_points"],
                "cluster_centers": result.get("cluster_centers", []),
                "meta": result["meta"]
            }},
            upsert=True
        )
        return jsonify(result), 200
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500