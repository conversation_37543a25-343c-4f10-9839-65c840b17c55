from flask import Blueprint, request, jsonify
from db import init_db
import pandas as pd
from bson import ObjectId
import Levenshtein
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from fuzzywuzzy import fuzz

# สร้าง Blueprint สำหรับ duplicate handling
duplicate_bp = Blueprint('duplicate', __name__)

# MongoDB
from flask import Flask
app = Flask(__name__)
mongo = init_db(app)

# ------------------- เรียง ouput ให้สวยเฉยๆ -------------------
def compact_lists_in_json(json_str):
    # แทนที่ lists ที่มีเว้นบรรทัด ให้มาเป็นบรรทัดเดียว
    # โดยจับ pattern: "key": [ ...multiple lines... ]
    # ใช้ regex แบบง่าย
    pattern = re.compile(r'(\[\s*(?:[^\[\]]+?)\s*\])', re.MULTILINE)

    def replacer(match):
        # เอา list เดิมมาแทน \n และ space เยอะๆ ด้วย space เดียว
        content = match.group(0)
        single_line = re.sub(r'\s+', ' ', content)
        return single_line

    return pattern.sub(replacer, json_str)

# --------- Apply Remove Duplicate  ---------
def apply_remove_duplicate(df, duplicate_columns, keep_option="first"):
    # จัดการข้อมูลซ้ำตามคอลัมน์และตัวเลือก keep ที่กำหนด keep_option: "first", "last", หรือ "remove_all"

    if isinstance(duplicate_columns, str):
        duplicate_columns = [duplicate_columns]

    keep_opt = keep_option.lower()
    if keep_opt in ["remove_all", "none"]: # ลบทั้งหมด
        df_result = df[~df.duplicated(subset=duplicate_columns, keep=False)]
    elif keep_opt == "last": # เก็บแถวสุดท้าย
        df_result = df.drop_duplicates(subset=duplicate_columns, keep="last")
    else:  # default: keep first เก็บแถวแรก
        df_result = df.drop_duplicates(subset=duplicate_columns, keep="first")

    return df_result

# --------- Apply Aggregate Duplicates  ---------
def apply_aggregate_duplicates(df, groupby_cols, agg_dict):
    # ตรวจสอบว่า groupby columns มีอยู่ใน DataFrame
    missing_cols = [col for col in groupby_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"Missing groupby columns: {missing_cols}")

    # ตรวจสอบว่า agg columns และ methods ถูกต้อง
    valid_aggs = {"mean", "sum", "count", "first", "last", "min", "max", "median", "std"}

    for col, agg in agg_dict.items():
        if col not in df.columns:
            raise ValueError(f"Missing agg column: {col}")
        if isinstance(agg, str):
            if agg not in valid_aggs:
                raise ValueError(f"Invalid aggregation method '{agg}' for column '{col}'")
        elif isinstance(agg, list):
            for a in agg:
                if a not in valid_aggs:
                    raise ValueError(f"Invalid aggregation method '{a}' in list for column '{col}'")
        else:
            raise ValueError(f"Aggregation method for column '{col}' must be string or list of strings")

    # ทำการ aggregate ด้วย pandas groupby + agg
    aggregated_df = df.groupby(groupby_cols).agg(agg_dict).reset_index()

    return aggregated_df

# --------- Apply Similarity and Clustering Duplicate  ---------
def similarity_and_find_clusters(values, method="levenshtein", threshold=0.8):
    def calc_similarity(str1, str2):
        str1 = str(str1)
        str2 = str(str2)
        if method == "levenshtein":
            max_len = max(len(str1), len(str2))
            if max_len == 0:
                return 1.0
            dist = Levenshtein.distance(str1, str2)
            return 1 - dist / max_len
        elif method == "jaccard":
            set1 = set(str1.lower().split())
            set2 = set(str2.lower().split())
            intersection = len(set1.intersection(set2))
            union = len(set1.union(set2))
            if union == 0:
                return 0.0
            return intersection / union
        else:
            raise ValueError("method must be 'levenshtein' or 'jaccard'")
    
    n = len(values)
    visited = [False] * n
    clusters = []

    def dfs(i, cluster):
        stack = [i]
        while stack:
            node = stack.pop()
            if not visited[node]:
                visited[node] = True
                cluster.append(node)
                for j in range(n):
                    if not visited[j] and calc_similarity(values[node], values[j]) >= threshold:
                        stack.append(j)

    for i in range(n):
        if not visited[i]:
            cluster = []
            dfs(i, cluster)
            clusters.append({
                "indices": cluster,
                "values": [values[idx] for idx in cluster]
            })

    return clusters

# --------- Apply Handle Duplicates in Text ---------
def handle_duplicate_text(df, columns, method="cosine", threshold=0.8):
    combined_texts = df[columns].astype(str).agg(" | ".join, axis=1).tolist()
    df["__combined__"] = combined_texts

    def remove_similar_texts(texts):
        n = len(texts)
        visited = [False] * n
        unique_indices = []

        if method == "cosine":
            tfidf = TfidfVectorizer().fit_transform(texts)
            sim_matrix = cosine_similarity(tfidf)

            for i in range(n):
                if not visited[i]:
                    unique_indices.append(i)
                    for j in range(i + 1, n):
                        if sim_matrix[i][j] >= threshold:
                            visited[j] = True

        elif method == "fuzzy":
            for i in range(n):
                if not visited[i]:
                    unique_indices.append(i)
                    for j in range(i + 1, n):
                        ratio = fuzz.token_set_ratio(texts[i], texts[j]) / 100
                        if ratio >= threshold:
                            visited[j] = True
        else:
            raise ValueError("method must be 'cosine' or 'fuzzy'")

        return unique_indices

    indices_to_keep = remove_similar_texts(combined_texts)
    df_cleaned = df.iloc[indices_to_keep].drop(columns=["__combined__"])
    return df_cleaned

# --------- Route Remove Duplicate ---------
@duplicate_bp.route('/duplicate/remove', methods=['POST'])
def handle_duplicates():
    dataset_id = request.json.get("dataset_id")
    keep_option = request.json.get("keep", "first")
    duplicate_columns = request.json.get("columns")

    if not dataset_id or not duplicate_columns:
        return jsonify({"msg": "dataset_id and columns are required"}), 400

    # โหลดข้อมูลจาก MongoDB
    current_doc = mongo.db.current.find_one({"dataset_id": ObjectId(dataset_id)})
    if current_doc:
        df = pd.DataFrame(current_doc["data"])
        previous_data = current_doc["data"]
    else:
        previous_doc = mongo.db.previous.find_one({"dataset_id": ObjectId(dataset_id)})
        if not previous_doc:
            return jsonify({"msg": "Dataset not found"}), 404
        df = pd.DataFrame(previous_doc["data"])
        previous_data = previous_doc["data"]

    # ตรวจสอบคอลัมน์
    if isinstance(duplicate_columns, str):
        duplicate_columns = [duplicate_columns]
    missing_cols = [col for col in duplicate_columns if col not in df.columns]
    if missing_cols:
        return jsonify({"msg": f"Missing columns: {', '.join(missing_cols)}"}), 400

    # ลบข้อมูลที่ซ้ำกัน
    processed_df = apply_remove_duplicate(df, duplicate_columns, keep_option)

    # เก็บข้อมูลก่อนการลบซ้ำไปที่ previous
    mongo.db.previous.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": previous_data}},
        upsert=True
    )

    # อัปเดตข้อมูลใน current หลังการลบซ้ำ
    mongo.db.current.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": processed_df.to_dict(orient="records")}},
        upsert=True
    )

    return jsonify({
        "duplicate_handling_data": processed_df.to_dict(orient="records")
    }), 200

# --------- Route Aggregate Duplicates ---------
@duplicate_bp.route('/duplicate/aggregate', methods=['POST'])
def aggregate_duplicates():
    data = request.json
    dataset_id = data.get("dataset_id")
    groupby_columns = data.get("groupby_columns") # คอลัมน์ที่ต้องการจัดกลุ่ม
    agg_columns = data.get("agg_columns") # คอลัมน์ที่ต้องการรวมข้อมูลและวิธีการรวม "mean", "sum", "count", "first", "last", "min", "max", "median", "std"

    # ตรวจสอบว่ามีการส่งพารามิเตอร์ครบถ้วนหรือไม่
    if not dataset_id or not groupby_columns or not agg_columns:
        return jsonify({"msg": "dataset_id, groupby_columns, and agg_columns are required"}), 400
    
    # ดึงข้อมูลจาก MongoDB
    current_doc = mongo.db.current.find_one({"dataset_id": ObjectId(dataset_id)})
    if current_doc:
        df = pd.DataFrame(current_doc["data"])
        previous_data = current_doc["data"]
    else:
        previous_doc = mongo.db.previous.find_one({"dataset_id": ObjectId(dataset_id)})
        if not previous_doc:
            return jsonify({"msg": "Dataset not found"}), 404
        df = pd.DataFrame(previous_doc["data"])
        previous_data = previous_doc["data"]

    try:
        # ใช้ฟังก์ชัน apply_aggregate_duplicates เพื่อทำการรวมข้อมูล
        result_df = apply_aggregate_duplicates(df, groupby_columns, agg_columns)
    except ValueError as e:
        return jsonify({"msg": str(e)}), 400
    
    # อัปเดตข้อมูลใน MongoDB
    mongo.db.previous.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": previous_data}},
        upsert=True
    )

    mongo.db.current.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": result_df.to_dict(orient="records")}},
        upsert=True
    )

    # ส่งคืนข้อมูลที่ถูกจัดกลุ่มและรวมแล้ว
    return jsonify({
        "aggregated_data": result_df.to_dict(orient="records")
    }), 200

# --------- Route Similarity-Cluster Duplicate ---------
@duplicate_bp.route('/duplicate/similarity-cluster', methods=['POST'])
def similarity_cluster_route():
    data = request.json
    dataset_id = data.get("dataset_id")
    columns = data.get("columns") # คอลัมน์ที่ใช้ในคำนวณ similarity
    method = data.get("method", "levenshtein").lower() # วิธีการคำนวณ Levenshtein หรือ Jaccard
    threshold = data.get("threshold", 0.8) # ปรับค่า threshold ตามต้องการ

    # ตรวจสอบว่ามีการส่งพารามิเตอร์ครบถ้วนหรือไม่
    if not dataset_id or not columns or not isinstance(columns, list):
        return jsonify({"msg": "dataset_id and columns (as list) are required"}), 400
    if method not in ["levenshtein", "jaccard"]:
        return jsonify({"msg": "method must be 'levenshtein' or 'jaccard'"}), 400
    if not (0 <= threshold <= 1):
        return jsonify({"msg": "threshold must be between 0 and 1"}), 400

    # ดึงข้อมูลจาก MongoDB
    current_doc = mongo.db.current.find_one({"dataset_id": ObjectId(dataset_id)})
    if current_doc:
        df = pd.DataFrame(current_doc["data"])
        previous_data = current_doc["data"]
    else:
        previous_doc = mongo.db.previous.find_one({"dataset_id": ObjectId(dataset_id)})
        if not previous_doc:
            return jsonify({"msg": "Dataset not found"}), 404
        df = pd.DataFrame(previous_doc["data"])
        previous_data = previous_doc["data"]

    # ตรวจสอบว่า columns ที่เลือกมีใน DataFrame หรือไม่
    missing_cols = [col for col in columns if col not in df.columns]
    if missing_cols:
        return jsonify({"msg": f"Missing columns: {', '.join(missing_cols)}"}), 400
    
    # รวมค่าของคอลัมน์ที่เลือกและทำการคำนวณ similarity
    combined_values = df[columns].astype(str).agg(' | '.join, axis=1).tolist()
    clusters = similarity_and_find_clusters(combined_values, method=method, threshold=threshold)

    # เก็บเฉพาะรายการแรกของแต่ละกลุ่ม
    indices_to_keep = set()
    for cluster in clusters:
        if cluster["indices"]:
            indices_to_keep.add(cluster["indices"][0])

    # ทำการล้างข้อมูลโดยเก็บเฉพาะแถวที่เลือก
    df_cleaned = df.iloc[sorted(indices_to_keep)].reset_index(drop=True)

    # อัปเดตข้อมูลใน MongoDB
    mongo.db.previous.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": previous_data}},
        upsert=True
    )

    mongo.db.current.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {
            "data": df_cleaned.to_dict(orient="records")
        }},
        upsert=True
    )

    return jsonify({
        "total_clusters": len(clusters),  # จำนวนกลุ่มทั้งหมด
        "total_rows_before": len(df), # จำนวนแถวก่อนลบ
        "total_rows_after": len(df_cleaned), # จำนวนแถวหลังลบ
        "removed_rows": len(df) - len(df_cleaned), # จำนวนแถวที่ถูกลบ
        "kept_indices": sorted(indices_to_keep), # ดัชนีของแถวที่เก็บ
        "cleaned_data": df_cleaned.to_dict(orient="records") # ข้อมูลที่ทำการลบแล้ว
    }), 200

# --------- Route Handle Duplicates in Text ---------
@duplicate_bp.route('/duplicate/text', methods=['POST'])
def handle_duplicate_text_route():
    data = request.json
    dataset_id = data.get("dataset_id")
    columns = data.get("columns") # คอลัมน์ที่ใช้ในการตรวจสอบความคล้ายคลึงของข้อความ
    method = data.get("method", "cosine").lower() # วิธีการคำนวณ Cosine หรือ Fuzzy
    threshold = data.get("threshold", 0.8) # ปรับค่า threshold ตามต้องการ

    # ตรวจสอบว่ามีการส่งพารามิเตอร์ครบถ้วนหรือไม่    
    if not dataset_id or not columns or not isinstance(columns, list):
        return jsonify({"msg": "dataset_id and columns (list) are required"}), 400
    if method not in ["cosine", "fuzzy"]:
        return jsonify({"msg": "method must be 'cosine' or 'fuzzy'"}), 400
    if not (0 <= threshold <= 1):
        return jsonify({"msg": "threshold must be between 0 and 1"}), 400
    
    # ดึงข้อมูลจาก MongoDB
    current_doc = mongo.db.current.find_one({"dataset_id": ObjectId(dataset_id)})
    if current_doc:
        df = pd.DataFrame(current_doc["data"])
        previous_data = current_doc["data"]
    else:
        previous_doc = mongo.db.previous.find_one({"dataset_id": ObjectId(dataset_id)})
        if not previous_doc:
            return jsonify({"msg": "Dataset not found"}), 404
        df = pd.DataFrame(previous_doc["data"])
        previous_data = previous_doc["data"]

    # ตรวจสอบว่า columns ที่เลือกมีใน DataFrame หรือไม่
    missing_cols = [col for col in columns if col not in df.columns]
    if missing_cols:
        return jsonify({"msg": f"Missing columns: {', '.join(missing_cols)}"}), 400

    try:
        # ใช้ฟังก์ชัน handle_duplicate_text เพื่อลบข้อมูลที่คล้ายกัน
        cleaned_df = handle_duplicate_text(df, columns=columns, method=method, threshold=threshold)
    except Exception as e:
        return jsonify({"msg": str(e)}), 500
    
    # อัปเดตข้อมูลใน MongoDB
    mongo.db.previous.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": previous_data}},
        upsert=True
    )

    mongo.db.current.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {"data": cleaned_df.to_dict(orient="records")}},
        upsert=True
    )

    return jsonify({
        "total_rows_before": len(df), # จำนวนแถวก่อนลบ
        "total_rows_after": len(cleaned_df), # จำนวนแถวหลังลบ
        "cleaned_data": cleaned_df.to_dict(orient="records") # ข้อมูลที่ทำการลบแล้ว
    }), 200