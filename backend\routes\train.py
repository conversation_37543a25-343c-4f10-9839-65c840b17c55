from flask import Blueprint, request, jsonify
from db import init_db
from bson import ObjectId
import pandas as pd
import joblib
import os
import uuid
import pytz
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import numpy as np

# นำเข้าโมเดลจาก scikit-learn
from sklearn.linear_model import LogisticRegression, LinearRegression, Ridge, Lasso
from sklearn.svm import SVC
from sklearn.naive_bayes import MultinomialNB
from sklearn.tree import DecisionTreeClassifier, DecisionTreeRegressor
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor, GradientBoostingClassifier, AdaBoostClassifier
from sklearn.neighbors import KNeighborsClassifier, KNeighborsRegressor
from sklearn.neural_network import MLPClassifier

train_bp = Blueprint('train', __name__)

# MongoDB
from flask import Flask
app = Flask(__name__)
mongo = init_db(app)

# กำหนด timezone เป็น Bangkok
bangkok_tz = pytz.timezone('Asia/Bangkok')

# ฟังก์ชันนี้ใช้ในการดึงเวลาในรูปแบบ timestamp
def get_current_time():
    return datetime.now(bangkok_tz).strftime('%Y-%m-%dT%H:%M:%SZ')

# แมปชื่อโมเดลกับคลาสจาก scikit-learn
model_mapping = {
    "logistic_regression": LogisticRegression,
    "svc": SVC,
    "naive_bayes": MultinomialNB,
    "decision_tree_classifier": DecisionTreeClassifier,
    "random_forest_classifier": RandomForestClassifier,
    "knn_classifier": KNeighborsClassifier,
    "linear_regression": LinearRegression,
    "ridge": Ridge,
    "lasso": Lasso,
    "decision_tree_regressor": DecisionTreeRegressor,
    "random_forest_regressor": RandomForestRegressor,
    "knn_regressor": KNeighborsRegressor,
    "gradient_boosting_classifier": GradientBoostingClassifier,
    "adaboost_classifier": AdaBoostClassifier,
    "mlp_classifier": MLPClassifier
}

# การสร้างโมเดลตามชื่อที่ระบุ
def create_model(name, params):
    ModelClass = model_mapping.get(name)
    if not ModelClass:
        raise ValueError("Invalid model name")
    return ModelClass(**params)

@train_bp.route('/train-full', methods=['POST'])
def train_full():
    try:
        # รับข้อมูลจากผู้ใช้ที่ส่งมาใน request
        data = request.get_json()

        dataset_id = data.get('dataset_id')  # ใช้ ObjectId แทน filename
        use_cv = data.get('use_cross_validation', False) # ใช้ cross-validation หรือไม่
        split_ratio = data.get('split_ratio') # อัตราส่วนในการแบ่งข้อมูล (train, test, validation)
        target_column = data.get('target_column') # คอลัมน์ที่ต้องการทำนาย
        model_name = data.get('model_name') # ชื่อของโมเดลที่ต้องการฝึก
        model_params = data.get('model_params', {})  # พารามิเตอร์สำหรับโมเดล

        # ตรวจสอบว่ามีข้อมูลที่จำเป็นครบถ้วนหรือไม่
        if not dataset_id or not target_column or not model_name:
            return jsonify({"error": "Missing required fields"}), 400
        
        # กำหนดตำแหน่งในการเก็บโมเดลที่ฝึกเสร็จแล้ว
        BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        MODEL_FOLDER = os.path.join(BASE_DIR, 'models')
        os.makedirs(MODEL_FOLDER, exist_ok=True)

        # ค้นหาข้อมูล dataset จาก MongoDB โดยใช้ ObjectId
        current_doc = mongo.db.current.find_one({"dataset_id": ObjectId(dataset_id)})
        if current_doc:
            df = pd.DataFrame(current_doc["data"])
        else:
            previous_doc = mongo.db.previous.find_one({"dataset_id": ObjectId(dataset_id)})
            if not previous_doc:
                return jsonify({"error": "Dataset not found"}), 404
            df = pd.DataFrame(previous_doc["data"])

        # ตรวจสอบว่า target_column อยู่ใน dataset หรือไม่
        if target_column not in df.columns:
            return jsonify({"error": f"Column '{target_column}' not found in dataset"}), 400
        
        # แยกข้อมูลออกเป็น X (features) และ y (target)
        X = df.drop(columns=[target_column])
        y = df[target_column]

        try:
            parts = [int(p.strip()) for p in split_ratio.split(":")]
        except Exception as e:
            return jsonify({"error": f"Invalid split_ratio format: {str(e)}"}), 400

        # แบ่งข้อมูล train, validation, test ด้วย cross-validation (ถ้าเลือก)
        if use_cv:
            if len(parts) != 3:
                return jsonify({"error": "CV split must have 3 parts"}), 400

            test_ratio = parts[2] / sum(parts)
            val_ratio = parts[1] / sum(parts[:2])

            X_temp, X_test, y_temp, y_test = train_test_split(X, y, test_size=test_ratio, random_state=42)
            X_train, X_val, y_train, y_val = train_test_split(X_temp, y_temp, test_size=val_ratio, random_state=42)
        else:
            if len(parts) != 2:
                return jsonify({"error": "Non-CV split must have 2 parts"}), 400

            test_ratio = parts[1] / sum(parts)
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_ratio, random_state=42)
            X_val, y_val = None, None

        # สร้างโมเดลและฝึก
        model = create_model(model_name, model_params)
        model.fit(X_train, y_train)

        # ทำนายผลจากข้อมูล test
        y_pred = model.predict(X_test)

        # คำนวณ metrics ในเเต่ละโมเดล
        if model_name in ['linear_regression', 'ridge', 'lasso', 'decision_tree_regressor', 'random_forest_regressor', 'knn_regressor']:
            metrics = {
                "mae": mean_absolute_error(y_test, y_pred),
                "mse": mean_squared_error(y_test, y_pred),
                "rmse": np.sqrt(mean_squared_error(y_test, y_pred)),
                "r2": r2_score(y_test, y_pred)
            }
        else:
            metrics = {
                "accuracy": accuracy_score(y_test, y_pred),
                "precision": precision_score(y_test, y_pred, average='weighted', zero_division=0),
                "recall": recall_score(y_test, y_pred, average='weighted', zero_division=0),
                "f1": f1_score(y_test, y_pred, average='weighted', zero_division=0)
            }

        # สร้างชื่อไฟล์ model เป็น {modeltype}{timestamp}_{shortuuid}.joblib
        timestamp = datetime.now(bangkok_tz).strftime('%Y-%m-%d_%H-%M-%S')
        short_id = str(uuid.uuid4())[:8]
        model_file_name = f"{model_name}_{timestamp}_{short_id}.joblib"
        model_path = os.path.join(MODEL_FOLDER, model_file_name)
        joblib.dump(model, model_path)

        # อัปเดตข้อมูล train/test/val ใน datasets_split
        mongo.db.datasets_split.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {
            "train": X_train.to_dict(orient="records"),
            "test": X_test.to_dict(orient="records"),
            "validation": X_val.to_dict(orient="records") if X_val is not None else None
        }},
        upsert=True
        )

        # อัปเดตข้อมูล metrics และ model path ใน datasets_model
        mongo.db.datasets_model.update_one(
        {"dataset_id": ObjectId(dataset_id)},
        {"$set": {
            "metrics": metrics,
            "model_path": model_path,
            "target_column": target_column,
        }},
        upsert=True
        )

        return jsonify({
            "message": "Model trained and dataset saved to MongoDB",
            "metrics": metrics,
            "model_file": model_path
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500
    