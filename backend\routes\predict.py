from flask import Blueprint, request, jsonify
from db import init_db
import joblib
import os
import pandas as pd
from bson import ObjectId

predict_bp = Blueprint('predict', __name__)

# MongoDB
from flask import Flask
app = Flask(__name__)
mongo = init_db(app)

@predict_bp.route('/columns', methods=['GET'])
def get_columns():
    try:
        # รับ ObjectId จาก query params
        dataset_id = request.args.get('dataset_id')
        # ตรวจสอบว่ามีการส่ง dataset_id หรือไม่
        if not dataset_id:
            return jsonify({"error": "dataset_id is required"}), 400

        # โหลดข้อมูล model และ train
        model_doc = mongo.db.datasets_model.find_one({"dataset_id": ObjectId(dataset_id)})
        split_doc = mongo.db.datasets_split.find_one({"dataset_id": ObjectId(dataset_id)})

        # หากไม่พบข้อมูล model หรือ split ให้แสดงข้อผิดพลาด
        if not model_doc or not split_doc:
            return jsonify({"error": "Model or Split data not found"}), 404
        
        # ดึงข้อมูล model path และ target_column
        model_path = model_doc.get('model_path')
        target_column = model_doc.get('target_column')
        train_data = split_doc.get('train')

        # ตรวจสอบว่าไฟล์โมเดลมีอยู่ในเครื่องหรือไม่
        if not os.path.exists(model_path):
            return jsonify({"error": "Model file not found"}), 404
        if not train_data:
            return jsonify({"error": "Train data not found"}), 404

        # ดึงชื่อคอลัมน์
        columns = list(pd.DataFrame(train_data).columns)
        if target_column in columns:
            columns.remove(target_column)

        return jsonify({"columns": columns}), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@predict_bp.route('/predict', methods=['POST'])
def predict():
    try:
        # รับข้อมูลจากผู้ใช้ผ่าน request body
        data = request.get_json()
        dataset_id = data.get('dataset_id')
        input_data = data.get('input_data')

        # ตรวจสอบว่า dataset_id และ input_data ถูกส่งมาหรือไม่
        if not dataset_id or not input_data:
            return jsonify({"error": "dataset_id and input_data are required"}), 400

        # โหลด model และ target column
        model_doc = mongo.db.datasets_model.find_one({"dataset_id": ObjectId(dataset_id)})
        if not model_doc:
            return jsonify({"error": "Model info not found"}), 404

        # ดึงข้อมูล model path และ target_column
        model_path = model_doc.get('model_path')
        target_column = model_doc.get('target_column')

        # ตรวจสอบว่าไฟล์โมเดลมีอยู่หรือไม่
        if not model_path or not os.path.exists(model_path):
            return jsonify({"error": "Model file not found"}), 404
        
        # โหลดโมเดลที่บันทึกไว้
        model = joblib.load(model_path)

        # แปลง input_data เป็น DataFrame เพื่อใช้ในการทำนาย
        input_df = pd.DataFrame([input_data])

        # predict
        prediction = model.predict(input_df)

        # เก็บผลลัพธ์
        prediction_data = {
            "input_data": input_data,
            "prediction": prediction.tolist() # แปลงผลลัพธ์เป็น list ก่อนเก็บ
        }

        mongo.db.datasets_predictions.update_one(
            {"dataset_id": ObjectId(dataset_id)},
            {"$set": {"predictions": prediction_data,}}, # อัปเดตข้อมูลการทำนายในฐานข้อมูล
        upsert=True
        )

        # ส่งผลลัพธ์การทำนายกลับไปยังผู้ใช้
        return jsonify({
            "prediction": prediction.tolist(), # ส่งผลลัพธ์ที่ predict เสร็จ
            "target_column": target_column # ส่งคืนชื่อ target_column
        }), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500