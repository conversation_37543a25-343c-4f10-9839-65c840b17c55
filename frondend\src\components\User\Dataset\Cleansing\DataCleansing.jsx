import React from "react";
import RelevantFeature from "./relavantFeature";
import OrdinalSorter from "./OrdinalSorter";
import FeatureClassification from "./Feature-classification";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./Tabs";
import Missing_value from "./Missing_Value/Missing_value";
import Outlier from "./Outlier/Outlier";
import Typo from "./Typo/Typo";
import Duplicate from "./Duplicate/Duplicate";
import Encoding from "./Encoding/Encoding";
import Scaling from "./Scaling/Scaling";
import Dimensional_reduction from "./Dimensional_Reduction/Dimensional_reduction";

const DataCleansing = () => {
  const [activeTab, setActiveTab] = React.useState("missing-value");

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold text-gray-800">Data Cleansing</h1>
      <RelevantFeature />
      <FeatureClassification />
      <OrdinalSorter />
      <Tabs
        defaultValue="missing-value"
        className="space-y-4"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList>
          <TabsTrigger value="missing-value">Missing value</TabsTrigger>
          <TabsTrigger value="outlier">Outlier</TabsTrigger>
          <TabsTrigger value="typo">Typo</TabsTrigger>
          <TabsTrigger value="duplicate">Duplicate</TabsTrigger>
          <TabsTrigger value="encoding">Encoding</TabsTrigger>
          <TabsTrigger value="scaling">Scaling</TabsTrigger>
          <TabsTrigger value="dimensional-reduction">
            Dimensional reduction
          </TabsTrigger>
        </TabsList>
        <TabsContent value="missing-value">
          <Missing_value />
        </TabsContent>
        <TabsContent value="outlier">
          <Outlier />
        </TabsContent>
        <TabsContent value="typo">
          <Typo />
        </TabsContent>
        <TabsContent value="duplicate">
          <Duplicate />
        </TabsContent>
        <TabsContent value="encoding">
          <Encoding />
        </TabsContent>
        <TabsContent value="scaling">
          <Scaling />
        </TabsContent>
        <TabsContent value="dimensional-reduction">
          <Dimensional_reduction />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DataCleansing;
