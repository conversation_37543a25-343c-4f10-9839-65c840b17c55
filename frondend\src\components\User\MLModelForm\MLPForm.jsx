import React, { useState, useEffect } from "react";

const MLPForm = ({ onParamsChange }) => {
    const [params, setParams] = useState({
        hidden_layer_size: 100,
        activation: "relu",
        solver: "adam",
    });

    useEffect(() => {
        onParamsChange(params);
    }, [params, onParamsChange]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setParams((prevParams) => ({
            ...prevParams,
            [name]: value,
        }));
    };

    return (
        <div className="space-y-2 p-4 bg-white rounded shadow">

            <div>
                <label className="block text-sm font-medium">hidden_layer_size:</label>
                <input
                    type="number"
                    name="hidden_layer_size"
                    value={params.hidden_layer_size}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="10"
                />
            </div>

            <div>
                <label className="block text-sm font-medium">activation:</label>
                <select
                    name="activation"
                    value={params.activation}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="identity">identity</option>
                    <option value="logistic">logistic</option>
                    <option value="tanh">tanh</option>
                    <option value="relu">relu</option>
                </select>
            </div>

            <div>
                <label className="block text-sm font-medium">solver:</label>
                <select
                    name="solver"
                    value={params.solver}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="lbfgs">lbfgs</option>
                    <option value="sgd">sgd</option>
                    <option value="adam">adam</option>
                </select>
            </div>
        </div>
    );
};

export default MLPForm;
